<template>
    <div class="header-actions">
        <button class="export-btn" @click="exportReport">导出报告</button>
    </div>
    <div class="task-report" id="task-report" ref="reportRef">
        <h1 class="report-title">{{ taskInfo.job_name }}任务执行报告</h1>
        <div class="report-divider"></div>

        <!-- 任务信息部分 -->
        <h2 class="section-title">任务信息</h2>
        <div class="info-section">
            <div class="info-row">
                <div class="info-item">
                    <span class="label">无人机名称:</span>
                    <span class="value">{{ taskInfo.drone_name }}</span>
                </div>
                <div class="info-item">
                    <span class="label">所属机场:</span>
                    <span class="value">{{ taskInfo.dock_name }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">计划名称:</span>
                    <span class="value">{{ taskInfo.job_name }}</span>
                </div>
                <div class="info-item">
                    <span class="label">计划类型:</span>
                    <span class="value">{{ formatTaskType(taskInfo) }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">无人机SN:</span>
                    <span class="value">{{ taskInfo.drone_sn }}</span>
                </div>
                <div class="info-item">
                    <span class="label">失联动作:</span>
                    <span class="value">{{ formatLostAction(taskInfo) }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">执行状态:</span>
                    <span class="value">{{ formatTaskStatus(taskInfo).text }}</span>
                </div>
                <div class="info-item">
                    <span class="label">执行失败原因:</span>
                    <span class="value">{{ taskInfo.code ? getCodeMessage(taskInfo.code) : '无' }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">创建人:</span>
                    <span class="value">{{ taskInfo.username }}</span>
                </div>
                <div class="info-item">
                    <span class="label">媒体上传状态:</span>
                    <span class="value">{{ formatMediaTaskStatus(taskInfo).text }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">返航高度(m):</span>
                    <span class="value">{{ taskInfo.rth_altitude }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">任务开始时间:</span>
                    <span class="value">{{ formatTaskTime(taskInfo.begin_time) }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">任务结束时间:</span>
                    <span class="value">{{ formatTaskTime(taskInfo.end_time) }}</span>
                </div>
            </div>
        </div>

        <div class="section-divider"></div>

        <!-- 航线信息部分 -->
        <h2 class="section-title">航线信息</h2>
        <div class="info-section">
            <div class="info-row">
                <div class="info-item">
                    <span class="label">航线名称:</span>
                    <span class="value">{{ taskInfo.file_name }}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">航线类型:</span>
                    <span class="value">{{ formatWaylineType(taskInfo) }}</span>
                </div>
            </div>
            <div v-if="taskInfo?.wayline_type === 0">
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">航线长度:</div>
                        <div class="value">{{ timeData && timeData.distance ? timeData.distance + 'm' : '' }}</div>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">预计执行时间:</div>
                        <div class="value">{{ timeData && timeData.workTime ? timeData.workTime : '' }}</div>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">航点:</div>
                        <div class="value">{{ timeData && timeData.pointCount ? timeData.pointCount : '' }}</div>
                    </div>
                </div>
            </div>
            <div v-if="taskInfo?.wayline_type !== 0">
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">面积:</div>
                        <div class="value">{{ timeData?.PolygonArea?.toFixed(2) + '㎡' }}</div>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">航线长度:</div>
                        <div class="value">{{ timeData?.distance?.toFixed(1) + 'm' }}</div>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">预计总时长:</div>
                        <div class="value">{{ formatSeconds(timeData?.time?.toFixed(0)) || 0 }}</div>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <div class="label">预计总照片数:</div>
                        <div class="value">{{ timeData?.pallcont || 0 }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section-divider"></div>

        <!-- 报警信息部分 -->
        <h2 class="section-title" v-if="alarmData.length > 0">报警信息</h2>
        <div class="alert-section">
            <!-- 报警项 -->
            <div class="alert-item" v-for="item in alarmData" :key="item.id">
                <div class="alert-header">
                    <div class="alert-info">
                        <div><span class="label">发生时间:</span> <span class="value">{{ item.happenTime }}</span></div>
                        <div><span class="label">算法类型:</span> <span class="value">{{ item.algoName }}</span></div>
                    </div>
                    <div class="alert-status">
                        <div><span class="label">处理状态:</span> <span class="value">{{ handleStatus(item.handleStatus)
                        }}</span></div>
                        <div><span class="label">处理人:</span> <span class="value">{{ item.handleUserName }}</span></div>
                    </div>
                </div>
                <div class="alert-images">
                    <div class="image-container">
                        <img :src="item.srcpicUrl" alt="报警图片" />
                    </div>
                    <div class="image-container">
                        <img :src="item.picUrl" alt="报警图片" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { exportToPDF } from './pdf';
import { ref, onMounted } from 'vue';
import { getWaylineJobDetail, getWayLineDetail } from '@/api/wayline'
import { getList } from "@/api/alarmManage/index";
import { useFormatTask } from './use-format-task'
import { getErrorMessage } from '@/utils/error-code/index'

const reportRef = ref(null);
const props = defineProps({
    jobId: {
        type: String,
        required: true
    },
    fileId: {
        type: String,
        required: true
    }
});
const { formatTaskType, formatTaskTime, formatLostAction, formatTaskStatus, formatMediaTaskStatus, formatWaylineType } = useFormatTask()

function getCodeMessage(code) {
    return getErrorMessage(code) + `（错误码: ${code}）`;
}

// 添加时间格式化函数
function formatSeconds(seconds) {
    if (!seconds) return '-';
    const s = parseInt(seconds);
    const hours = Math.floor(s / 3600);
    const minutes = Math.floor((s % 3600) / 60);
    const remainingSeconds = s % 60;

    let result = '';
    if (hours > 0) {
        result += `${hours}小时`;
    }
    if (minutes > 0 || hours > 0) {
        result += `${minutes}分`;
    }
    result += `${remainingSeconds}秒`;

    return result;
}

const taskInfo = ref({});
const timeData = ref({});
const alarmData = ref([]);

const handleStatus = (status) => {
    if (status === 0) {
        return '未处理';
    } else if (status === 1) {
        return '处理中';
    } else if (status === 2) {
        return '已处理';
    }
}

onMounted(async () => {
    const res = await getWaylineJobDetail(props.jobId)
    taskInfo.value = res.data.list[0];

    const res2 = await getWayLineDetail(props.fileId)

    if (res2.data.waylineData.template_type !== 'waypoint') {
        timeData.value = res2.data['3ddata'];
    } else {
        timeData.value = res2.data.wayLineCount;
    }

    const res3 = await getList(1, -1, {
        cid: taskInfo.value.drone_sn,
        startTime: taskInfo.value.begin_time,
        endTime: taskInfo.value.end_time
    })
    alarmData.value = res3.data.data.records;
});

const exportReport = async () => {
    if (!reportRef.value) {
        console.error('报告元素不存在');
        return;
    }

    try {
        await exportToPDF(reportRef.value, `${taskInfo.value.job_name}任务执行报告.pdf`);
    } catch (error) {
        console.error('导出报告失败:', error);
    }
};
</script>

<style lang="scss" scoped>
.header-actions {
    display: flex;
    // justify-content: flex-end;
    // margin-bottom: 15px;

    .export-btn {
        background-color: #1890ff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &:hover {
            background-color: #40a9ff;
        }
    }
}

.task-report {
    padding: 20px;
    background-color: #fff;
    min-height: 100vh;
    font-family: Arial, sans-serif;

    .report-title {
        text-align: center;
        font-size: 20px;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;
    }

    .report-divider {
        margin: 0 auto 20px;
        height: 1px;
        background-color: #ddd;
        width: 100%;
    }

    .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 15px 0;
    }

    .section-divider {
        margin: 20px auto;
        height: 1px;
        background-color: #ddd;
        width: 100%;
    }

    .info-section {
        background-color: #fff;
        border-radius: 4px;
        padding: 10px 0;

        .info-row {
            display: flex;
            padding: 5px 10px;

            .info-item {
                flex: 1;
                display: flex;

                .label {
                    color: #666;
                    min-width: 120px;
                    font-size: 14px;
                }

                .value {
                    color: #333;
                    font-size: 14px;
                }
            }
        }
    }

    .alert-section {
        .alert-item {
            background-color: #fff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #eee;

            .alert-header {
                display: flex;
                margin-bottom: 15px;

                .alert-info,
                .alert-status {
                    flex: 1;

                    div {
                        margin-bottom: 5px;

                        .label {
                            color: #666;
                            font-size: 14px;
                        }

                        .value {
                            color: #333;
                            font-size: 14px;
                        }
                    }
                }
            }

            .alert-images {
                display: flex;
                gap: 15px;

                .image-container {
                    flex: 1;
                    height: 216px;
                    border: 1px solid #ddd;
                    border-radius: 4px;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                }
            }
        }
    }
}
</style>