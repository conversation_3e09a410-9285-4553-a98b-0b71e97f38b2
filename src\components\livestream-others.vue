<template>
  <div class="flex-column flex-justify-start flex-align-center">
    <div id="rtcPlayer" style="height: 370px;">
      <jessibucaPlayer ref="jessibuca" :visible.sync="true" :videoUrl="videoUrl"
                       :hasAudio="true" fluent autoplay live></jessibucaPlayer>
    </div>

    <p class="fz18" style="color:#fff">直播源选择</p>
    <div class="flex-row flex-justify-center flex-align-center mt10">
      <template v-if="liveState && isDockLive">
        <span class="mr10" style="color: white;">镜头:</span>
        <a-radio-group v-model:value="lensSelected" button-style="solid">
          <a-radio-button v-for="lens in lensList" :key="lens" :value="lens">
            {{ displayMode[lens] }}
          </a-radio-button>
        </a-radio-group>
      </template>
      <template v-else>
        <a-select
            style="width: 150px"
            placeholder="选择直播类型"
            @select="onLiveTypeSelect"
            v-model:value="livetypeSelected"
        >
          <a-select-option v-for="item in liveTypeList" :key="item.label" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
            class="ml10"
            style="width: 150px"
            placeholder="选择设备"
            v-model:value="droneSelected"
        >
          <a-select-option
              v-for="item in droneList"
              :key="item.value"
              :value="item.value"
              @click="onDroneSelect(item)"
          >{{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
            class="ml10"
            style="width: 150px"
            placeholder="选择相机"
            v-model:value="cameraSelected"
        >
          <a-select-option
              v-for="item in cameraList"
              :key="item.value"
              :value="item.value"
              @click="onCameraSelect(item)"
          >{{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <a-select
          class="ml10"
          style="width: 150px"
          placeholder="选择画质"
          @select="onClaritySelect"
          v-model:value="claritySelected"
      >
        <a-select-option v-for="item in clarityList" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>

    <div class="mt20">
      <p class="fz10" v-if="livetypeSelected == 2">
        Please use VLC media player to play the RTSP livestream !!!
      </p>
      <p class="fz10" v-if="livetypeSelected == 2">RTSP Parameter:{{ rtspData }}</p>
    </div>

    <div class="flex-row flex-justify-center flex-align-center">
      <a-button v-if="liveState && isDockLive" type="primary" large @click="onSwitch">切换镜头</a-button>
      <a-button v-else type="primary" large @click="onStart">播放</a-button>
      <a-button class="ml20" type="primary" large @click="onStop">停止</a-button>
      <a-button class="ml20" type="primary" large @click="onUpdateQuality">切换画质</a-button>
      <a-button v-if="liveState && isDockLive" class="ml20" type="primary" large @click="checkPosition(0)">舱内</a-button>
      <a-button v-if="liveState && isDockLive" class="ml20" type="primary" large @click="checkPosition(1)">舱外</a-button>
      <a-button v-if="!liveState" class="ml20" type="primary" large @click="onRefresh">刷新直播能力</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {message} from 'ant-design-vue';
import {ElMessage} from 'element-plus';
import jessibucaPlayer from "@/views/video/common/jessibuca.vue";
import {onMounted, ref, computed, onBeforeUnmount} from 'vue';
import {onBeforeRouteLeave} from 'vue-router';
import {CURRENT_CONFIG as config} from '@/config/dijconfig';
import {
  changeLivestreamLens,
  getLiveCapacity,
  setLivestreamQuality,
  startLivestream,
  stopLivestream,
} from '@/api/manage';
import {cameraChange} from '../api/livestream/index.js';
import {getRoot} from '@/root';
import store from '@/store/';
import EventBus from '@/event-bus';

const root = getRoot();
const jessibuca = ref()

let videoUrl = computed(() => {
  return store.state.common.videoUrl
})

interface SelectOption {
  value: any;
  label: string;
  more?: any;
}

const liveTypeList: SelectOption[] = [
  // {
  //   value: 1,
  //   label: 'RTMP',
  // },
  // {
  //   value: 2,
  //   label: 'RTSP',
  // },
  {
    value: 3,
    label: 'GB28181',
  },
  // {
  //   value: 4,
  //   label: 'WEBRTC',
  // },
];

const clarityList: SelectOption[] = [
  {
    value: 0,
    label: '自适应',
  },
  {
    value: 1,
    label: '流畅',
  },
  {
    value: 2,
    label: '标清',
  },
  {
    value: 3,
    label: '高清',
  },
  {
    value: 4,
    label: '超清',
  },
];

// 镜头类型枚举
const displayMode = {
  normal: '默认',
  wide: '广角',
  zoom: '变焦',
  ir: '红外',
};

const livestreamSource = ref();
const droneList = ref();
const cameraList = ref();
const videoList = ref();
const droneSelected = ref();
const cameraSelected = ref();
const videoSelected = ref();
const claritySelected = ref();
const videoId = ref();
const liveState = ref<boolean>(false);
const livetypeSelected = ref(3);
const rtspData = ref();
const lensList = ref<string[]>([]);
const lensSelected = ref<String>();
const isDockLive = ref(false);
const nonSwitchable = 'normal';
let webrtc: any = null;

const onRefresh = async () => {
  droneList.value = [];
  cameraList.value = [];
  videoList.value = [];
  droneSelected.value = null;
  cameraSelected.value = null;
  videoSelected.value = null;
  await getLiveCapacity({}).then(res => {
    if (res.code === 0) {
      if (res.data === null) {
        return;
      }
      const resData: Array<[]> = res.data;
      livestreamSource.value = resData;
      const temp: Array<SelectOption> = [];
      if (livestreamSource.value) {
        livestreamSource.value.forEach((ele: any) => {
          temp.push({label: ele.name + '-' + ele.sn, value: ele.sn, more: ele.cameras_list});
        });
        droneList.value = temp;
      }
    }
  }).catch(error => {
    message.error(error);
  });
};

const checkPosition = async (type) => {
  if (
      livetypeSelected.value == null ||
      droneSelected.value == null ||
      cameraSelected.value == null ||
      claritySelected.value == null
  ) {
    message.warn('请选择直播参数!')
    return
  }
  const params = {
    video_id: videoId.value,
    camera_position: type,
  }
  const {data} = await cameraChange(params)
  if (data.code == 0) {
    setTimeout(() => {
      ElMessage({
        type: 'success',
        message: '操作成功！',
      })
    }, 1500);
  } else {
    setTimeout(() => {
      ElMessage({
        type: 'error',
        message: '操作失败！',
      })
    }, 1500);
  }
};

onMounted(() => {
  onRefresh();
  EventBus.on('closeLive', (data) => {
    onStop()
  })
});
onBeforeUnmount(() => {
    EventBus.off('closeLive')
})

const onStart = async () => {
  // console.log('Param:', livetypeSelected.value, droneSelected.value, cameraSelected.value, videoSelected.value, claritySelected.value)
  const timestamp = new Date().getTime().toString()
  videoId.value =
      droneSelected.value + '/' + cameraSelected.value + '/' + (videoSelected.value || nonSwitchable + '-0')
  if (
      livetypeSelected.value == null ||
      droneSelected.value == null ||
      cameraSelected.value == null ||
      claritySelected.value == null
  ) {
    message.warn('请选择直播参数')
    return
  }
  let liveURL = ''
  switch (livetypeSelected.value) {
    case 1: {
      // RTMP
      liveURL = config.rtmpURL + timestamp
      break
    }
    case 2: {
      // RTSP
      liveURL = `userName=${config.rtspUserName}&password=${config.rtspPassword}&port=${config.rtspPort}`
      break
    }
    case 3: {
      liveURL = `serverIP=${config.gbServerIp}&serverPort=${config.gbServerPort}&serverID=${config.gbServerId}&agentID=${config.gbAgentId}&agentPassword=${config.gbPassword}&localPort=${config.gbAgentPort}&channel=${config.gbAgentChannel}`
      break
    }
    case 4: {
      break
    }
    default:
      break
  }
  await startLivestream({
    url: liveURL,
    video_id: videoId.value,
    url_type: livetypeSelected.value,
    video_quality: claritySelected.value
  }).then(res => {
    if (res.code !== 0) {
      return
    }
    if (livetypeSelected.value === 3) {
      if (document.location.href.includes('https')) {
        // 正式
        const url = res.data.wss_flv
        store.commit('VIDEO_URL', url);
      } else {
        // 本地
        const url = res.data.ws_flv
        store.commit('VIDEO_URL', url);
      }
    }
    liveState.value = true
  }).catch(err => {
    console.error(err);
  })
};

const onStop = () => {
  store.commit('VIDEO_URL', '');
  videoId.value =
      droneSelected.value +
      '/' +
      cameraSelected.value +
      '/' +
      (videoSelected.value || nonSwitchable + '-0');
  if (
      livetypeSelected.value == null ||
      droneSelected.value == null ||
      cameraSelected.value == null ||
      claritySelected.value == null
  ) {
    return
  }
  // stopLivestream({
  //   video_id: videoId.value,
  // }).then(res => {
  //   if (res.code === 0) {
  //     message.success(res.message);
      liveState.value = false;
      lensSelected.value = undefined;
  //   }
  // });
};

const onUpdateQuality = () => {
  if (!liveState.value) {
    message.info('请先开启直播.');
    return;
  }
  setLivestreamQuality({
    video_id: videoId.value,
    video_quality: claritySelected.value,
  }).then(res => {
    if (res.code === 0) {
      message.success('将清晰度设置为' + clarityList[claritySelected.value].label);
    }
  });
};

const onLiveTypeSelect = (val: any) => {
  livetypeSelected.value = val;
};

const onDroneSelect = (val: SelectOption) => {
  droneSelected.value = val.value;
  const temp: Array<SelectOption> = [];
  cameraList.value = [];
  cameraSelected.value = undefined;
  videoSelected.value = undefined;
  videoList.value = [];
  lensList.value = [];
  if (!val.more) {
    return;
  }
  val.more.forEach((ele: any) => {
    temp.push({label: ele.name, value: ele.index, more: ele.videos_list});
  });
  cameraList.value = temp;
};

const onCameraSelect = (val: SelectOption) => {
  cameraSelected.value = val.value;
  const result: Array<SelectOption> = [];
  videoSelected.value = undefined;
  videoList.value = [];
  lensList.value = [];
  if (!val.more) {
    return;
  }
  val.more.forEach((ele: any) => {
    result.push({label: ele.type, value: ele.index, more: ele.switch_video_types});
  });
  videoList.value = result;
  if (videoList.value.length === 0) {
    return;
  }
  const firstVideo: SelectOption = videoList.value[0];
  videoSelected.value = firstVideo.value;
  lensList.value = firstVideo.more;
  lensSelected.value = firstVideo.label;
  isDockLive.value = lensList.value?.length > 0;
};

const onVideoSelect = (val: SelectOption) => {
  videoSelected.value = val.value;
  lensList.value = val.more;
  lensSelected.value = val.label;
};

const onClaritySelect = (val: any) => {
  claritySelected.value = val;
};

const onSwitch = () => {
  if (lensSelected.value === undefined || lensSelected.value === nonSwitchable) {
    message.info(nonSwitchable + ' 不可切换, 请选择要切换的镜头.', 8);
    return;
  }
  changeLivestreamLens({
    video_id: videoId.value,
    video_type: lensSelected.value,
  }).then(res => {
    if (res.code === 0) {
      message.success('成功切换直播摄像头.');
    }
  });
};

onBeforeRouteLeave(() => {
  // 路由即将改变前执行的操作
  if (liveState.value) {
    onStop();
  }
});
</script>

<style lang="scss" scoped>
@import '@/styles/dij/index.scss';
</style>