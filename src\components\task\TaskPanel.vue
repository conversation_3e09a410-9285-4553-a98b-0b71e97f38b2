<template>
  <div class="plan-panel-wrapper" :style="componentStyle">
    <a-config-provider :locale="zhCN">
      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="计划名称">
            <a-input v-model:value="searchForm.jobName" placeholder="请输入计划名称" allowClear />
          </a-form-item>
          <a-form-item label="设备SN">
            <a-select v-model:value="searchForm.sn" placeholder="请选择设备SN" style="width: 250px" mode="multiple"
              allowClear>
              <a-select-option v-for="device in deviceList" :key="device.device_sn" :value="device.device_sn">
                {{ device.device_name }} ({{ device.device_sn }})
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="航线类型">
            <a-select v-model:value="searchForm.waylineType" placeholder="请选择航线类型" style="width: 120px" allowClear>
              <a-select-option v-for="(text, value) in WaylineTypeMap" :key="value" :value="Number(value)">
                {{ text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="计划类型">
            <a-select v-model:value="searchForm.task_type" placeholder="请选择计划类型" style="width: 120px" allowClear>
              <a-select-option v-for="(text, value) in TaskTypeMap" :key="value" :value="value">
                {{ text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="执行状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 120px" allowClear>
              <a-select-option v-for="(text, value) in searchStatusOptions" :key="value" :value="Number(value)">
                {{ text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间区间">
            <a-range-picker v-model:value="searchForm.timeRange" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </div>
      <a-table size="small" row-key="job_id" :data-source="plansData.data" :columns="columns"
        :pagination="paginationProp" :scroll="{ x: 1500, y: 550 }" @change="refreshData">
        <!-- 执行时间 -->
        <template #duration="{ record }">
          <div class="flex-row">
            <div>{{ formatTaskDate(record.begin_at) }}</div>
            <div class="time-range">
              <div class="time-range-item">
                <div class="mr10">{{ formatTaskTime(record.begin_at) }}</div>
                <div class="mr10">{{ formatTaskTime(record.end_at) }}</div>
              </div>
              <div class="time-range-item">
                <div>{{ formatTaskTime(record.run_at) }}</div>
                <div>{{ formatTaskTime(record.completed_at) }}</div>
              </div>
            </div>
          </div>
        </template>
        <!-- 执行状态 -->
        <template #status="{ record }">
          <div>
            <div class="flex-display flex-align-center">
              <div class="circle-icon" :style="{ backgroundColor: formatTaskStatus(record).color }"></div>
              {{ formatTaskStatus(record).text }}
              <a-tooltip v-if="!!record.code" placement="bottom" arrow-point-at-center>
                <template #title>
                  <div>{{ getCodeMessage(record.code) }}</div>
                </template>
                <exclamation-circle-outlined class="ml5" :style="{ color: commonColor.WARN, fontSize: '16px' }" />
              </a-tooltip>
            </div>
            <div v-if="record.status === TaskStatus.executing">
              <a-progress :percent="record.progress || 0" />
            </div>
          </div>
        </template>
        <!-- 任务类型 -->
        <template #task_type="{ record }">
          <div>{{ formatTaskType(record) }}</div>
        </template>
        <!-- 失控动作 -->
        <template #lostAction="{ record }">
          <div>{{ formatLostAction(record) }}</div>
        </template>
        <!-- 航线名称 -->
        <template #fileName="{ record }">
          <div>{{ formatWaylineName(record) }}</div>
        </template>
        <!-- 设备名称 -->
        <template #dock_name="{ record }">
          <div>{{ formatDockName(record) }}</div>
        </template>
        <!-- 航线类型 -->
        <template #waylineType="{ record }">
          <div>{{ formatWaylineType(record) }}</div>
        </template>
        <!-- 媒体上传状态 -->
        <template #media_upload_status="{ record }">
          <div>
            <div class="flex-display flex-align-center">
              <div class="circle-icon" :style="{ backgroundColor: formatMediaTaskStatus(record).color }"></div>
              {{ formatMediaTaskStatus(record).text }}
            </div>
            <div class="pl15">
              {{ formatMediaTaskStatus(record).number }}
              <!-- <a-tooltip v-if="formatMediaTaskStatus(record).status === MediaStatus.ToUpload" placement="bottom"
                arrow-point-at-center>
                <template #title>
                  <div>Upload now</div>
                </template>
                <UploadOutlined class="ml5" :style="{ color: commonColor.BLUE, fontSize: '16px' }"
                  @click="onUploadMediaFileNow(record.job_id)" />
              </a-tooltip> -->
            </div>
          </div>
        </template>
        <!-- 操作 -->
        <template #action="{ record }">
          <div class="action-area">
            <a-popconfirm v-if="record.status === TaskStatus.waiting" title="您确定要删除航线任务吗？" ok-text="是" cancel-text="否"
              @confirm="onDeleteTask(record.job_id)">
              <div style="cursor: pointer;margin-right: 10px;">删除</div>
            </a-popconfirm>
            <!--v-if="record.status === 3"-->
            <!-- <a style="margin-right: 10px;color: #1890ff;" @click="rowClick(record)">飞行记录</a><br> -->
            <a-button type="link" @click="rowClick(record)">飞行记录</a-button>
            <!-- <a style="margin-right: 10px;" @click="rowClick1(record)">飞行记录视频</a><br> -->
            <!-- <a style="margin-right: 10px;" @click="rowClick2(record)">查看报告</a><br> -->
            <a-button type="link" @click="rowClick3(record)">飞行媒体</a-button>
            <a style="margin-right: 10px;color: #1890ff;" v-if="record.is_break_point === true"
              @click="onBreakPointJob(record)">断点续飞</a>
            <!--            <a-popconfirm v-if="record.status === TaskStatus.Carrying"-->
            <!--                          title="您确定要挂起吗?"-->
            <!--                          ok-text="是"-->
            <!--                          cancel-text="否"-->
            <!--                          @confirm="onSuspendTask(record.job_id)"-->
            <!--            >-->
            <!--              <span style="cursor: pointer;margin-right: 10px;">挂起</span>-->
            <!--            </a-popconfirm>-->
            <!--            <a-popconfirm v-if="record.status === TaskStatus.Paused"-->
            <!--                          title="您确定要继续吗？"-->
            <!--                          ok-text="是"-->
            <!--                          cancel-text="否"-->
            <!--                          @confirm="onResumeTask(record.job_id)"-->
            <!--            >-->
            <!--              <div style="cursor: pointer;margin-right: 10px;">继续</div>-->
            <!--            </a-popconfirm>-->
          </div>
        </template>
      </a-table>
    </a-config-provider>

    <!-- 任务报告弹窗 -->
    <a-modal v-model:visible="reportModalVisible" title="" width="900px" :footer="null" destroyOnClose>
      <task-report :job-id="currentJobId" :file-id="currentFileId"></task-report>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { reactive, onMounted, onBeforeUnmount, defineEmits, inject, computed, ref } from 'vue'
import { message } from 'ant-design-vue'
import { deleteTask, updateTaskStatus, UpdateTaskStatus, getWaylineJobs, Task, uploadMediaFileNow, getWayLineDetail, getHistoryTrajectory, breakPointJob } from '@/api/wayline'
import { getDeviceListByProjectUuid } from '@/api/workspace/index'
import { getWayLineList } from '@/api/workspace/index'
import { DockOnlineDevices } from '@/api/screen'
import { ELocalStorageKey } from '@/types/enums'
import { useFormatTask } from './use-format-task'
import { TaskStatus, TaskProgressInfo, TaskProgressStatus, TaskProgressWsStatusMap, MediaStatus, MediaStatusProgressInfo, TaskMediaHighestPriorityProgressInfo, TaskStatusMap, WaylineTypeMap, TaskTypeMap } from '@/types/task'
import { useTaskWsEvent } from './use-task-ws-event'
import { getErrorMessage } from '@/utils/error-code/index'
import { commonColor } from '@/utils/color'
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue'
import EventBus from '@/event-bus/'
import { getWorkspaceId } from '@/utils/storage'
import TaskReport from './TaskReport.vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { getProjectUuid } from '@/utils/storage'

// 搜索表单数据
const searchForm = reactive({
  jobName: '',
  status: undefined,
  sn: [],
  waylineType: undefined,
  task_type: undefined,
  timeRange: [
    // 今天0:00:00
    dayjs().add(-1, 'month').hour(0).minute(0).second(0),
    // 一个月后23:59:59
    dayjs().add(1, 'month').hour(23).minute(59).second(59)
  ],
})

const excludedStatuses = [TaskStatus.terminated, TaskStatus.paused];
const searchStatusOptions = Object.fromEntries(
  Object.entries(TaskStatusMap).filter(([key]) => !excludedStatuses.includes(Number(key)))
);

const deviceList = ref([])

const reportModalVisible = ref(false);
const currentJobId = ref('');
const currentFileId = ref('');
const route = useRoute();
const router = useRouter();

const componentStyle = computed(() => ({
  width: `${itemSize.value.width}px`,
  height: `${itemSize.value.height}px`,
}));

const itemSize = inject('itemSize', ref({ width: null, height: null }));

const emit = defineEmits(['updateData', 'showAmp', 'AmpJobId', 'AmpJobMedia', 'showPlanList', 'AmpJobVideo']);

const body = {
  page: 1,
  total: 0,
  page_size: 10
}
const paginationProp = reactive({
  pageSizeOptions: ['20', '50', '100'],
  showQuickJumper: true,
  showSizeChanger: true,
  pageSize: 100,
  current: 1,
  total: 0
})

const columns = [
  {
    title: '计划 | 实际时间',
    dataIndex: 'duration',
    width: 230,
    slots: { customRender: 'duration' },
  },
  {
    title: '执行状态',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '类型',
    dataIndex: 'task_type',
    width: 100,
    slots: { customRender: 'task_type' },
  },
  {
    title: '计划名称',
    dataIndex: 'name',
    width: 130,
    ellipsis: true
  },
  {
    title: '航线名称',
    dataIndex: 'file_name',
    width: 160,
    slots: { customRender: 'fileName' },
    ellipsis: true
  },
  // {
  //   title: '航线类型',
  //   dataIndex: 'wayline_type',
  //   width: 120,
  //   slots: { customRender: 'waylineType' },
  // },
  {
    title: '设备名称',
    dataIndex: 'dock_name',
    width: 120,
    slots: { customRender: 'dock_name' },
    ellipsis: true
  },
  // {
  //   title: '创建人',
  //   dataIndex: 'username',
  //   width: 100,
  // },
  {
    title: '媒体上传状态',
    key: 'media_upload_status',
    width: 140,
    slots: { customRender: 'media_upload_status' }
  },
  {
    title: '操作',
    // width: 220,
    // fixed: 'right',
    slots: { customRender: 'action' }
  }
]
type Pagination = any

const plansData = reactive({
  data: [] as Task[]
})

const { formatTaskType, formatTaskDate, formatTaskTime, formatLostAction, formatTaskStatus, formatMediaTaskStatus, formatWaylineType } = useFormatTask()

// 设备任务执行进度更新
function onTaskProgressWs(data: TaskProgressInfo) {
  const { bid, output } = data
  if (output) {
    const { status, progress } = output || {}
    const taskItem = plansData.data.find(task => task.job_id === bid)
    if (!taskItem) return
    if (status) {
      taskItem.status = TaskProgressWsStatusMap[status]
      // 执行中，更新进度
      if (status === TaskProgressStatus.Sent || status === TaskProgressStatus.inProgress) {
        taskItem.progress = progress?.percent || 0
      } else if ([TaskProgressStatus.Rejected, TaskProgressStatus.Canceled, TaskProgressStatus.Timeout, TaskProgressStatus.Failed, TaskProgressStatus.OK].includes(status)) {
        getPlans()
      }
    }
  }
}

//var polyline=null,passedPolyline=null,marker=null,lineArr=[];
let marker = null;      // 将marker声明为全局变量
let polyline = null;    // 将polyline声明为全局变量
let passedPolyline = null; // 将passedPolyline声明为全局变量
let lineArr = [];       // 初始化路径数组

const rowClick = (item) => {
  emit('showAmp', item.uuid);//航线
  emit('AmpJobId', item.uuid);//历史轨迹
  emit('showPlanList', false);
}

const rowClick1 = (item) => {
  emit('AmpJobVideo', item.uuid);//历史轨迹视频
  // emit('showPlanList', false);
}

const rowClick2 = (item) => {
  currentJobId.value = item.job_id;
  currentFileId.value = item.file_id;
  reportModalVisible.value = true;
  emit('showPlanList', true);
}

const rowClick3 = (item) => {
  emit('AmpJobMedia', item.uuid);//飞行媒体
}

const onBreakPointJob = (item) => {
  breakPointJob(item.job_id).then(res => {
    if (res.code === 0) {
      message.success('断点续飞成功')
    } else {
      message.error('断点续飞失败')
    }
  }).finally(() => {
    getPlans()
  })
}

function clearMarkersAndPolylines() {
  if (marker) {
    marker.setMap(null);
    marker.destroy(); // 销毁标记对象，确保所有资源被释放

    marker = null;
  }
  if (polyline) {
    polyline.setMap(null);
    polyline.destroy(); // 销毁多段线对象，确保所有资源被释放
    polyline = null;
  }
  if (passedPolyline) {
    passedPolyline.setMap(null);
    passedPolyline.destroy(); // 销毁辅助多段线对象
    passedPolyline = null;
  }
  //lineArr = []; // 清空轨迹数组
  // 清除任何可能存在的事件监听器
  if (marker && marker.off) {
    marker.off('moving');
  }
}

// 媒体上传进度更新
function onTaskMediaProgressWs(data: MediaStatusProgressInfo) {
  const { media_count: mediaCount, uploaded_count: uploadedCount, job_id: jobId } = data
  if (isNaN(mediaCount) || isNaN(uploadedCount) || !jobId) {
    return
  }
  const taskItem = plansData.data.find(task => task.job_id === jobId)
  if (!taskItem) return
  if (mediaCount === uploadedCount) {
    taskItem.uploading = false
  } else {
    taskItem.uploading = true
  }
  taskItem.media_count = mediaCount
  taskItem.uploaded_count = uploadedCount
}

function onoTaskMediaHighestPriorityWS(data: TaskMediaHighestPriorityProgressInfo) {
  const { pre_job_id: preJobId, job_id: jobId } = data
  const preTaskItem = plansData.data.find(task => task.job_id === preJobId)
  const taskItem = plansData.data.find(task => task.job_id === jobId)
  if (preTaskItem) {
    preTaskItem.uploading = false
  }
  if (taskItem) {
    taskItem.uploading = true
  }
}

function getCodeMessage(code: number) {
  return getErrorMessage(code) + `（错误码: ${code}）`
}

useTaskWsEvent({
  onTaskProgressWs,
  onTaskMediaProgressWs,
  onoTaskMediaHighestPriorityWS,
})

// 处理搜索按钮点击
function handleSearch() {
  // 重置分页到第一页
  paginationProp.current = 1
  body.page = 1
  getPlans()
}

// 重置搜索条件
function resetSearch() {
  // 重置表单
  Object.keys(searchForm).forEach(key => {
    if (key === 'status' || key === 'waylineType' || key === 'task_type') {
      searchForm[key] = undefined
    } else if (key === 'sn') {
      searchForm[key] = deviceList.value.map(device => device.device_sn)
    } else if (key === 'timeRange') {
      searchForm.timeRange = [
        dayjs().add(-1, 'month').hour(0).minute(0).second(0),
        dayjs().add(1, 'month').hour(23).minute(59).second(59)
      ]
    } else {
      searchForm[key] = ''
    }
  })

  // 重置分页
  paginationProp.current = 1
  body.page = 1

  // 重新加载数据
  getPlans()
}

onMounted(async () => {
  await getDeviceList()
  await getWayline()
  getPlans()
  EventBus.on('updateplan', getPlans) //收到通知
})

onBeforeUnmount(() => {
  EventBus.off('updateplan')

  // 清理路由参数，避免下次打开时携带
  if (route.query.jobId) {
    const query = { ...route.query }
    delete query.jobId
    router.replace({ query })
  }
})

function getPlans() {
  const routeId = route.query.jobId as string;

  // 构建搜索参数
  const searchParams: any = {}

  // 只添加有值的搜索条件
  Object.keys(searchForm).forEach(key => {
    if (key !== 'timeRange' && searchForm[key] !== '' && searchForm[key] !== undefined) {
      if (key === 'sn' && Array.isArray(searchForm[key]) && searchForm[key].length > 0) {
        searchParams[key] = searchForm[key]
      } else if (key !== 'sn') {
        searchParams[key] = searchForm[key]
      }
    }
  })
  console.log(searchParams)
  if (searchForm.timeRange && searchForm.timeRange.length === 2) {
    searchParams.begin_at = Math.floor(searchForm.timeRange[0].valueOf() / 1000) // 转换为秒级时间戳
    searchParams.end_at = Math.floor(searchForm.timeRange[1].valueOf() / 1000) // 转换为秒级时间戳
  }

  getWaylineJobs({
    ...body,
    ...(routeId ? { jobId: routeId } : {}),
    ...searchParams // 添加搜索条件到请求参数
  }).then(res => {
    if (res.code !== 200) {
      return
    }
    plansData.data = res.data
    // paginationProp.total = res.data.pagination.total
    // paginationProp.current = res.data.pagination.page
  })
}

function refreshData(page: Pagination) {
  body.page = page?.current!
  body.page_size = page?.pageSize!
  getPlans()
}

// 删除任务
async function onDeleteTask(jobId: string) {
  const { code } = await deleteTask(getWorkspaceId(), {
    job_id: jobId
  })
  if (code === 0) {
    message.success('删除任务成功')
    getPlans()
  }
}

// 挂起任务
async function onSuspendTask(jobId: string) {
  const { code } = await updateTaskStatus(getWorkspaceId(), {
    job_id: jobId,
    status: UpdateTaskStatus.Suspend
  })
  if (code === 0) {
    message.success('挂起任务成功')
    getPlans()
  }
}

// 解除挂起任务
async function onResumeTask(jobId: string) {
  const { code } = await updateTaskStatus(getWorkspaceId(), {
    job_id: jobId,
    status: UpdateTaskStatus.Resume
  })
  if (code === 0) {
    message.success('解除挂起任务成功')
    getPlans()
  }
}

// 立即上传媒体
async function onUploadMediaFileNow(jobId: string) {
  const { code } = await uploadMediaFileNow(getWorkspaceId(), jobId)
  if (code === 0) {
    message.success('上传媒体文件成功')
    getPlans()
  }
}

const waylineList = ref([])
async function getWayline() {
  const res = await getWayLineList()
  if (res.data.code === 200) {
    waylineList.value = res.data.data
  }
}

function formatWaylineName(record: Task) {
  const wayline = waylineList.value.find(wayline => wayline.id === record.wayline_uuid)
  return wayline?.name
}

function formatDockName(record: Task) {
  const dock = deviceList.value.find(dock => dock.device_sn === record.sn)
  return dock?.device_name
}

async function getDeviceList() {
  try {
    // const res = await DockOnlineDevices()
    const res = await getDeviceListByProjectUuid({
      projectUuid: getProjectUuid()
    })
    if (res.data.code === 200 && res.data.data) {
      deviceList.value = res.data.data.map(item => ({
        device_sn: item.gateway.sn,
        device_name: item.gateway.callsign
      }))
      searchForm.sn = deviceList.value.map(device => device.device_sn)
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.plan-panel-wrapper {
  width: 99%;

  .search-form {
    background-color: #fff;
    padding: 12px 16px;

    :deep(.ant-form-item) {
      margin-bottom: 12px;
    }
  }

  .plan-table {
    margin: 0 10px;
  }

  .action-area {
    &::v-deep {
      .ant-btn {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }

  .circle-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 3px;
    border-radius: 50%;
    vertical-align: middle;
    flex-shrink: 0;
  }
}

:deep(.ant-table-pagination.ant-pagination) {
  float: right;
  margin: 0;
  background: #fff;
}

::v-deep.ant-table-thead>tr>th {
  color: #749dee !important;
}

.time-range {
  display: flex;
  gap: 50px;
}

.time-range-item {
  position: relative;
  padding-left: 10px;
}

.time-range-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8px;
  bottom: 8px;
  border-left: 1.5px solid #888;
}

.time-range-item div:first-child::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 0;
  width: 5px;
  height: 1.5px;
  background-color: #888;
}

.time-range-item div:last-child::before {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 0px;
  width: 5px;
  height: 1.5px;
  background-color: #888;
}
</style>