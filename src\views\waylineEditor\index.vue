<template>
    <div class="fh2-container-wrapper">
        <!-- ******* 接入步骤 2 - START：提供组件所在的容器 ******* -->
        <div class="fh2-container">
            <div id="wayline-header"></div>
            <div class="project-details">
                <div id="project-app-container"></div>
                <div id="project-middle-container"></div>
                <div id="project-right-micro-app" class="right-micro-app">
                    <div class="maps-micro-app">
                        <div id="project-map-app-placeholder" class="map-app-placeholder">
                            <div id="map-app-global" class="map-app-container"></div>
                        </div>
                    </div>
                    <div id="wayline-app-container"></div>
                </div>
            </div>
        </div>
        <!-- ******* 接入步骤 2 - END：提供组件所在的容器 ******* -->

        <!-- <div class="btn-bar">
            <button v-if="showCesiumMapBtn" @click="addCustomCesiumData">地图定制</button>
            <button @click="changeTheme">样式定制</button>
        </div> -->
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { fh2Config } from '@/config/fh2.js';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const showCesiumMapBtn = ref(false);

function initialize() {
    if (!window.FH2) {
        console.error('FH2 is not available.');
        return;
    }

    // ******* 接入步骤 3 - START：使用组件前初始化配置 *******
    window.FH2.initConfig({
        serverUrl: fh2Config.serverUrl,
        wssUrl: fh2Config.wssUrl,
        hostUrl: fh2Config.hostUrl,
        prjId: fh2Config.prjId,
        projectToken: fh2Config.projectToken,
    });
    // ******* 接入步骤 3 - END：使用组件前初始化配置 *******

    // ******* 接入步骤 4 - START：加载组件 *******
    window.FH2.loadWayline("wayline-app-container", {
        wayline_id: route.query.id,
    });
    // ******* 接入步骤 4 - END：加载组件 *******

    // ******* 接入步骤 5（按需） - START：监听组件事件 *******
    window.FH2.subscribe('cesium-viewer-change', () => {
        if (window.FH2.cesiumViewer.global) {
            showCesiumMapBtn.value = true;
        }
    });
    window.FH2.subscribe('wayline-cancel', () => {
        router.back();
        console.log('取消保存航线');
    });
    window.FH2.subscribe('wayline-save', () => {
        router.back();
        console.log('保存航线');
    });
    window.FH2.subscribe('wayline-back', () => {
        router.back();
        console.log('退出航线编辑器');
    });
    // ******* 接入步骤 5（按需） - END：监听组件事件 *******
}

// ******* 接入步骤 6（按需） - START：自定义地图元素 *******
function addCustomCesiumData() {
    if (!window.FH2 || !window.FH2.cesiumViewer || !window.Cesium) {
        console.error("FH2 or Cesium is not initialized.");
        return;
    }
    for (const key in window.FH2.cesiumViewer) {
        if (Object.prototype.hasOwnProperty.call(window.FH2.cesiumViewer, key)) {
            window.FH2.cesiumViewer[key].entities.add({
                position: window.Cesium.Cartesian3.fromDegrees(113.93, 22.57, 50),
                label: {
                    text: "用户自定义文本",
                },
            });
        }
    }
}
// ******* 接入步骤 6（按需） - END：自定义地图元素 *******

// ******* 接入步骤 7（按需） - START：自定义主题 *******
function changeTheme() {
    document.body.classList.toggle('set-change-color');
}
// ******* 接入步骤 7（按需） - END：自定义主题 *******

onMounted(() => {
    // ******* 接入步骤 6（按需） - START：自定义主题 *******
    const styleLink = document.createElement('link');
    styleLink.id = 'custom-theme-style';
    styleLink.rel = 'stylesheet';
    styleLink.href = '/custom-style.css';
    document.head.appendChild(styleLink);
    // ******* 接入步骤 6（按需） - END：自定义主题 *******

    // ******* 接入步骤 1 - START：引入 paas.js *******
    const script = document.createElement('script');
    script.id = 'fh2-paas-script';
    script.src = `${fh2Config.hostUrl}/paas.js`;
    script.setAttribute('fh2', '');
    document.body.appendChild(script);

    script.onload = initialize;
    // ******* 接入步骤 1 - END：引入 paas.js *******
});

onUnmounted(() => {
    // Clean up on component unmount
    const script = document.getElementById('fh2-paas-script');
    if (script) {
        document.body.removeChild(script);
    }
    const styleLink = document.getElementById('custom-theme-style');
    if (styleLink) {
        document.head.removeChild(styleLink);
    }
    document.body.classList.remove('set-change-color');

    window.FH2.destroyWayline();
});
</script>

<style lang="scss" scoped>
.fh2-container-wrapper {
    height: 100%;
    width: 100%;
    margin: 0;
}

.btn-bar {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 9999;
    gap: 10px;
}

/* ******* 接入步骤 1 - START：添加组件容器样式 ******* */
.fh2-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

:deep(#project-app-container > div),
:deep(.map-app-container > div) {
    width: 100%;
    height: 100%;
}

.project-details {
    position: relative;
    width: 100%;
    display: flex;
    flex: 1;
    overflow: auto;
}

.project-details .right-micro-app {
    position: relative;
    display: flex;
    flex: 1;
}

.project-details .right-micro-app .maps-micro-app {
    display: flex;
    flex: 1;
}

.project-details .right-micro-app .map-app-placeholder {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
}

.project-details .right-micro-app .map-app-container {
    height: 100%;
}

:deep(.project-details .right-micro-app #wayline-app-container) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
/* ******* 接入步骤 1 - END：添加组件容器样式 ******* */
</style>