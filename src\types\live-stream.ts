export interface LiveStreamStatus {
  audioBitRate: number,
  dropRate: number,
  fps: number,
  jitter: number,
  quality: number,
  rtt: number,
  status: number,
  type: number,
  videoBitRate: number
}

export interface GB28181Param {
  serverIp: string,
  serverPort: string,
  serverId: string,
  agentId: string,
  password: string,
  agentPort: string,
  agentChannel: string
}

export interface RTSPParam {
  userName: string,
  password: string,
  port: string
}

export interface LiveConfigParam {
  params: number,
  type: any
}

export enum EVideoPublishType {
  VideoOnDemand = 'video-on-demand',
  VideoByManual = 'video-by-manual',
  VideoDemandAuxManual = 'video-demand-aux-manual'
}

export enum ELiveTypeValue {
  Unknown,
  Agora,
  RTMP,
  RTSP,
  GB28181
}

export enum ELiveTypeName {
  Unknown = 'Unknown',
  Agora = 'Agora',
  RTMP = 'RTMP',
  RTSP = 'RTSP',
  GB28181 = 'GB28181'
}

// 相机模式
export enum CameraMode {
  Photo = 0, // 拍照
  Video = 1, // 录像
}

// 镜头类型
export enum VideoType {
  NORMAL = 'normal',
  WIDE = 'wide',
  ZOOM = 'zoom',
  IR = 'ir'
}

// 镜头类型
export enum CameraType {
  WIDE = 'wide',
  ZOOM = 'zoom',
  IR = 'ir'
}

// 切换相机模式
export const CameraModeOptions = [
  {label: '拍照', value: 0},
  {label: '录像', value: 1},
  {label: '智能低光', value: 2},
  {label: '全景拍照', value: 3},
]

// 相机类型
export const CameraTypesOptions = [
  {label: '广角', value: 'wide'},
  {label: '变焦', value: 'zoom'},
]

// 曝光模式类型
export const CameraExposureModeOptions = [
  {label: '自动', value: 1},
  // {label: '快门优先曝光', value: 2}, //目前曝光模式设置只支持手动和自动
  // {label: '光圈优先曝光', value: 3},
  {label: '手动曝光', value: 4},
]

// 曝光值类型
export const CameraExposureValueOptions = [
  {label: '-5.0EV', value: 1},
  {label: '-4.7EV', value: 2},
  {label: '-4.3EV', value: 3},
  {label: '-4.0EV', value: 4},
  {label: '-3.7EV', value: 5},
  {label: '-3.3EV', value: 6},
  {label: '-3.0EV', value: 7},
  {label: '-2.7EV', value: 8},
  {label: '-2.3EV', value: 9},
  {label: '-2.0EV', value: 10},
  {label: '-1.7EV', value: 11},
  {label: '-1.3E', value: 12},
  {label: '-1.0EV', value: 13},
  {label: '-0.7EV', value: 14},
  {label: '-0.3EV', value: 15},
  {label: '0EV', value: 16},
  {label: '0.3EV', value: 17},
  {label: '0.7EV', value: 18},
  {label: '1.0EV', value: 19},
  {label: '1.3E', value: 20},
  {label: '1.7EV', value: 21},
  {label: '2.0EV', value: 22},
  {label: '2.3EV', value: 23},
  {label: '2.7EV', value: 24},
  {label: '3.0EV', value: 25},
  {label: '3.3EV', value: 26},
  {label: '3.7EV', value: 27},
  {label: '4.0EV', value: 28},
  {label: '4.3EV', value: 29},
  {label: '4.7EV', value: 30},
  {label: '5.0EV', value: 31},
  {label: 'FIXED', value: 255},
]

// 对焦模式类型
export const CameraFocusModeOptions = [
  {label: 'MF', value: 0},
  {label: 'AFS', value: 1},
  {label: 'AFC', value: 2},
]

// 测温模式类型
export const ThermometricTypesOptions = [
  {label: '关闭测温', value: 0},
  {label: '点测温', value: 1},
  {label: '区域测温', value: 2},
]

// 分屏类型
export const splitScreenOptions = [
  {label: '开', value: 1},
  {label: '关', value: 0},
]

// 变焦模式类型
export const CameraTypeOptions = [
  {label: '红外', value: 'ir'},
  {label: '广角', value: 'wide'},
  {label: '变焦', value: 'zoom'},
]

// 照片&视频存储设置
export const photoStorage = [
  {label: '当前', value: 'current'},
  {label: '可见光', value: 'vision'},
  {label: '红外', value: 'ir'},
]

export const ZoomCameraTypeOptions = [
  {label: CameraType.ZOOM, value: CameraType.ZOOM},
  {label: CameraType.IR, value: CameraType.IR},
]

export interface VideoListItem {
  video_index: string;
  video_type: VideoType;
  switchable_video_types?: Array<VideoType>;
}

export interface CameraListItem {
  available_video_number: number;
  camera_index: string;
  camera_name: string;
  coexist_video_number_max: number;
  video_list: VideoListItem[];
  // 自定义
  switchCamera?: boolean;
  content?: string;
  // 该camera由哪个控上报的
  camera_carrier_sns?: string[];
}

export interface DeviceListItem {
  sn: string;
  available_video_number: number;
  coexist_video_number_max: number;
  camera_list: CameraListItem[];
}

// export interface LiveCapacity {
//     available_video_number: number;
//     coexist_video_number_max: number;
//     device_list: DeviceListItem[];
// }

// export interface LiveStatus {
//     live_time: number; // 直播时间 该路码流已推流时间 unit: s
//     live_trendline: number; // 直播带宽的使用状态 代表直播性能趋势,0-4表示overuse，其中，数值越小，表示overuse程度越大，5表示normal状态，6~10表示underuse，其中，数值越大，表示有更多比例的带宽未能充分利用
//     video_id: string; // 直播码流标识符 某路在推视频码流的标识符，格式为 #{uav_sn}/#{camera_id}/#{video_index}
//     video_quality: number; // 直播码流的质量 0: 自动， 1: 流畅, 2: 高清， 3: 超清
//     error_status?: number; // 设备端当前状态，是错误码，需要匹配到文案上
//   }