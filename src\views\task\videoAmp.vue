<template>
  <div class="videoMap">
    <div :class="['video-container', { 'video-container-small': isMapExpanded }]">
      <video ref="videoPlayer"
             width="100%"
             height="100%"
             style="object-fit: fill;"
             controls
             preload="auto"
             controlslist="noplaybackrate"
      >
        <source :src="videoUrl" type="video/mp4">
      </video>
    </div>
    <div :class="['map-container', { 'map-container-expanded': isMapExpanded }]">
      <div id="mp-container" style="width: 100%;height: 100%;"></div>
    </div>
    <div class="fly_param">
      <div class="row">
        <i class="flyParamIcon"></i>
        <div class="flyParamTitle">经度:</div>
        <div class="text">{{ longitudeVd }}</div>
      </div>
      <div class="row">
        <i class="flyParamIcon"></i>
        <div class="flyParamTitle">绝对高度：</div>
        <div class="text">{{ heightVd }}</div>
      </div>
      <div class="row">
        <i class="flyParamIcon"></i>
        <div class="flyParamTitle">相对起飞点高度：</div>
        <div class="text">{{ elevationVd }}</div>
      </div>

      <div class="row">
        <i class="flyParamIcon"></i>
        <div class="flyParamTitle">纬度:</div>
        <div class="text">{{ latitudeVd }}</div>
      </div>
      <div class="row">
        <i class="flyParamIcon"></i>
        <div class="flyParamTitle">水平速度：</div>
        <div class="text">{{ horizontalSpeedVd }}</div>
      </div>
      <div class="row">
        <i class="flyParamIcon"></i>
        <div class="flyParamTitle">垂直速度：</div>
        <div class="text">{{ verticalSpeed }}</div>
      </div>
    </div>

    <div class="wnConversion" @click="transIon" v-if="!isMapExpanded">
      <a-tooltip placement="top">
        <template #title>
          <span>窗口转换</span>
        </template>
        <RetweetOutlined style="margin-top: 9px;color:#ffffff"/>
      </a-tooltip>
    </div>

    <div class="wnConversion1" @click="transIon" v-else>
      <a-tooltip placement="top">
        <template #title>
          <span>窗口转换</span>
        </template>
        <RetweetOutlined style="margin-top: 9px;color:#ffffff"/>
      </a-tooltip>
    </div>

    <div class="speed" v-if="!isMapExpanded">
      <a-popover>
        <template #content>
          <p @click="changePlaybackRate(1)" style="cursor: pointer;">1x</p>
          <p @click="changePlaybackRate(2)" style="cursor: pointer;">2x</p>
          <p @click="changePlaybackRate(3)" style="cursor: pointer;">3x</p>
          <p @click="changePlaybackRate(4)" style="cursor: pointer;">4x</p>
        </template>
        <text>{{ rate ? rate + 'x' : '倍速' }}</text>
      </a-popover>
    </div>

    <div class="speed1" v-else>
      <a-popover placement="left">
        <template #content>
          <p @click="changePlaybackRate(1)" style="cursor: pointer;">1x</p>
          <p @click="changePlaybackRate(2)" style="cursor: pointer;">2x</p>
          <p @click="changePlaybackRate(3)" style="cursor: pointer;">3x</p>
          <p @click="changePlaybackRate(4)" style="cursor: pointer;">4x</p>
        </template>
        <text>倍速</text>
      </a-popover>
    </div>

    <div class="close">
      <a-button type="primary" shape="circle" style="background: rgba(0, 0, 0, .5);" @click="closeExit">
        <template #icon>
          <CloseOutlined/>
        </template>
      </a-button>
    </div>
  </div>
</template>

<script>
import {getHistoryTrajectory} from '@/api/wayline';
import M30Vd from '@/assets/m30.png';
import {RetweetOutlined, CloseOutlined} from '@ant-design/icons-vue';
import { nextTick } from 'vue';

export default {
  props: ['VideoId'],
  components: {
    RetweetOutlined,
    CloseOutlined,
  },
  data() {
    return {
      videoUrl: '',
      videoPlayer: null,
      cesiumViewer: null,
      uavEntity: null,
      flightPath: null,
      lineArrVd: [],
      uavDetailsVd: [],
      durationVd: 500,
      longitudeVd: '',//经度
      latitudeVd: '',//纬度
      heightVd: '',//高度
      elevationVd: '',//相对起飞点高度
      horizontalSpeedVd: '',//水平速度
      verticalSpeed: '',//垂直速度
      FJindex: 0,//拖动进度条记录索引
      isMapExpanded: false, // 窗口转换状态变量
      rate: '',
      _pausedByCode: false,
      _playedByCode: false,
      onTimeUpdate: null,
    };
  },
  mounted() {
    this.initCesium();
    this.init();
  },
  beforeUnmount() {
    this.removeVideoEventListeners();
    if (this.cesiumViewer) {
      this.cleanEntities();
      this.cesiumViewer.destroy();
      this.cesiumViewer = null;
    }
  },
  methods: {
    initCesium() {
      // 直接在组件内初始化Cesium
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.rNfX8CPs-BUkQ6RImFXniBSIaefxrr62a7y6iIQN34w';
      
      // 创建Cesium查看器，使用mp-container容器
      this.cesiumViewer = new Cesium.Viewer('mp-container', {
        terrainProvider: Cesium.createWorldTerrain(),
        animation: false, // 禁用动画小部件
        baseLayerPicker: false, // 禁用图层选择器
        fullscreenButton: false, // 禁用全屏按钮
        vrButton: false, // 禁用VR按钮
        geocoder: false, // 禁用geocoder
        homeButton: false, // 禁用home按钮
        infoBox: false, // 禁用信息框
        sceneModePicker: false, // 禁用场景模式选择器
        selectionIndicator: false, // 禁用选择指示器
        timeline: false, // 禁用时间线
        navigationHelpButton: false, // 禁用导航帮助按钮
        navigationInstructionsInitiallyVisible: false, // 导航说明初始不可见
        scene3DOnly: true, // 只使用3D场景
      });
      
      // 开启深度测试
      if (this.cesiumViewer) {
        this.cesiumViewer.scene.globe.depthTestAgainstTerrain = true;
      }
      this.cesiumViewer.scene.postProcessStages.fxaa.enabled = true;
    },
    init() {
      this.rate = '';
      this.videoPlayer = this.$refs.videoPlayer;
      this.videoUrl = 'https://media.hzdssoft.com/api/hztech-mediaServer/api/cloud/record/displayFlightMp4?wayLineTaskId=' + this.VideoId;
      this.addVideoEventListeners();
      this.historyTrajectory();
    },
    addVideoEventListeners() {
      if (!this.videoPlayer) return;
      
      // 使用事件选项防止冲突
      const eventOptions = { once: false, passive: true };
      
      this.videoPlayer.addEventListener('play', this.onPlay, eventOptions);
      this.videoPlayer.addEventListener('pause', this.onPause, eventOptions);
      this.videoPlayer.addEventListener('ended', this.onEnded, eventOptions);
      this.videoPlayer.addEventListener('seeked', this.onSeeked, eventOptions);
    },
    removeVideoEventListeners() {
      if (!this.videoPlayer) return;
      
      this.videoPlayer.removeEventListener('play', this.onPlay);
      this.videoPlayer.removeEventListener('pause', this.onPause);
      this.videoPlayer.removeEventListener('ended', this.onEnded);
      this.videoPlayer.removeEventListener('seeked', this.onSeeked);
      
      if (this.onTimeUpdate) {
        this.videoPlayer.removeEventListener('timeupdate', this.onTimeUpdate);
      }
    },
    onPlay() {
      if (!this.videoPlayer) return;
      
      // The code to avoid event loop
      if (this._playedByCode) {
        this._playedByCode = false;
        return;
      }
      
      console.log('视频开始播放');
      // 同步轨迹动画的位置
      this.syncTrajectoryWithVideo();
      // 可以在这里添加其他逻辑，比如开始轨迹动画
      if (this.FJindex == 0) {
        this.startAnimation();
      } else {
        this.resumeAnimation();
      }
    },
    onPause() {
      if (!this.videoPlayer) return;
      
      // The code to avoid event loop
      if (this._pausedByCode) {
        this._pausedByCode = false;
        return;
      }
      
      console.log('视频暂停');
      // 可以在这里添加其他逻辑，比如暂停轨迹动画
      this.pauseAnimation();
    },
    onEnded() {
      if (!this.videoPlayer) return;
      
      console.log('视频播放结束');
      // 可以在这里添加其他逻辑，比如重置轨迹动画
      this.resetAnimation();
    },
    //拖动进度条时同步更新轨迹动画的位置
    onSeeked() {
      if (!this.videoPlayer) return;
      
      // 当视频拖动后，需要同步更新轨迹动画的位置
      const currentTime = this.videoPlayer.currentTime;
      const progressRatio = currentTime / this.videoPlayer.duration;
      const targetIndex = Math.max(0, Math.min(
        Math.floor(progressRatio * this.lineArrVd.length),
        this.lineArrVd.length - 1
      ));
      
      // 检查 targetIndex 是否合理并更新标记位置
      if (targetIndex < this.lineArrVd.length) {
        this.updateMarkerPosition(targetIndex);
      }
      
      // 同步轨迹动画的位置
      this.syncTrajectoryWithVideo();
    },
    //调整速率
    changePlaybackRate(rate) {
      this.rate = rate;
      if (this.videoPlayer) {
        this.videoPlayer.playbackRate = rate;
        this.durationVd = 500 / rate; // 更新动画持续时间
      }
      this.syncTrajectoryWithVideo(); // 更新倍速后同步轨迹和视频
    },
    //轨迹生成
    async historyTrajectory() {
      if (!this.cesiumViewer) {
        console.error('Cesium viewer尚未初始化');
        return;
      }
      
      // 清除现有实体
      this.cleanEntities();
      
      this.lineArrVd = []; // 清空之前的路径数组
      this.uavDetailsVd = [];

      await getHistoryTrajectory({jobId: this.VideoId}).then(data => {
        if (data.data.data && data.data.data !== '') {
          data.data.data.forEach(location => {
            this.lineArrVd.push([location.longitude, location.latitude, location.height || 0]);
            this.uavDetailsVd.push(location);
          });
        } else {
          console.warn('没有轨迹数据');
          return;
        }
      }).catch(error => {
        console.error('获取轨迹数据失败:', error);
        return;
      });

      if (this.lineArrVd.length === 0) {
        return;
      }

      // 创建无人机实体
      this.uavEntity = this.cesiumViewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          this.lineArrVd[0][0],
          this.lineArrVd[0][1],
          this.lineArrVd[0][2]
        ),
        billboard: {
          image: M30Vd,
          width: 32,
          height: 32,
          scaleByDistance: new Cesium.NearFarScalar(1000, 1, 20000, 0.5)
        }
      });

      // 创建飞行路径
      const positions = this.lineArrVd.map(point => 
        Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2])
      );

      this.flightPath = this.cesiumViewer.entities.add({
        polyline: {
          positions: positions,
          width: 3,
          material: Cesium.Color.fromCssColorString('#0aed8b')
        }
      });

      // 计算整个飞行路径的边界范围
      const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);
      
      // 使用flyToBoundingSphere方法自动计算最佳视角以查看整个路径
      this.cesiumViewer.camera.flyToBoundingSphere(boundingSphere, {
        offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-45), boundingSphere.radius * 2),
        duration: 1 // 快速过渡
      });

      // 更新初始位置信息
      this.updatePositionInfo(0);
    },
    //开始
    startAnimation() {
      if (!this.uavEntity || this.lineArrVd.length === 0) return;
      
      // 设置初始位置
      this.updateMarkerPosition(0);
      
      // 视频播放时，根据视频时间更新位置
      this.syncTrajectoryWithVideo();
    },
    //轨迹动画暂停
    pauseAnimation() {
      // 在视频事件处理中已经处理暂停
    },
    //轨迹动画继续
    resumeAnimation() {
      // 继续轨迹动画的逻辑，在视频事件中处理
    },
    //轨迹动画结束
    resetAnimation() {
      this.updateMarkerPosition(0);
    },
    //更新位置信息
    updatePositionInfo(index) {
      if (index < this.uavDetailsVd.length) {
        this.longitudeVd = this.lineArrVd[index][0];
        this.latitudeVd = this.lineArrVd[index][1];
        this.heightVd = this.uavDetailsVd[index].height;
        this.elevationVd = this.uavDetailsVd[index].elevation;
        this.horizontalSpeedVd = this.uavDetailsVd[index].horizontal_speed;
        this.verticalSpeed = this.uavDetailsVd[index].vertical_speed;
      }
    },
    //更新轨迹动画位置
    updateMarkerPosition(index) {
      if (!this.uavEntity || !this.lineArrVd[index]) return;
      
      this.FJindex = index;
      
      // 更新无人机位置
      this.uavEntity.position = Cesium.Cartesian3.fromDegrees(
        this.lineArrVd[index][0],
        this.lineArrVd[index][1],
        this.lineArrVd[index][2]
      );
      
      // 更新位置信息
      this.updatePositionInfo(index);
    },
    //修改视频播放控制
    syncTrajectoryWithVideo() {
      if (!this.videoPlayer || this.lineArrVd.length === 0) {
        return; // 如果videoPlayer未初始化或轨迹数据为空，直接返回
      }
      
      // 移除之前的监听器以避免重复
      if (this.onTimeUpdate) {
        this.videoPlayer.removeEventListener('timeupdate', this.onTimeUpdate);
        this.onTimeUpdate = null;
      }
      
      // 添加新的监听器
      this.onTimeUpdate = () => {
        const currentTime = this.videoPlayer.currentTime;
        if (currentTime > 0) {
          const videoDuration = this.videoPlayer.duration;
          const progressRatio = currentTime / videoDuration;
          const targetIndex = Math.max(0, Math.min(
            Math.floor(progressRatio * this.lineArrVd.length),
            this.lineArrVd.length - 1
          ));
          
          // 使用requestAnimationFrame保证平滑更新且不干扰视频播放
          requestAnimationFrame(() => {
            this.updateMarkerPosition(targetIndex);
          });
        }
      };
      
      this.videoPlayer.addEventListener('timeupdate', this.onTimeUpdate);
    },
    transIon() {
      this.isMapExpanded = !this.isMapExpanded;
      
      // 地图改变大小后需要刷新
      nextTick(() => {
        if (this.cesiumViewer) {
          this.cesiumViewer.resize();
          
          // 如果有路径，确保可见性
          if (this.flightPath) {
            const positions = this.lineArrVd.map(point => 
              Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2])
            );
            const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);
            
            this.cesiumViewer.camera.flyToBoundingSphere(boundingSphere, {
              offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-45), boundingSphere.radius * 2),
              duration: 1
            });
          }
        }
      });
    },
    cleanEntities() {
      if (this.cesiumViewer) {
        if (this.uavEntity) {
          this.cesiumViewer.entities.remove(this.uavEntity);
          this.uavEntity = null;
        }
        if (this.flightPath) {
          this.cesiumViewer.entities.remove(this.flightPath);
          this.flightPath = null;
        }
      }
    },
    closeExit() {
      this.videoUrl = '';
      this.removeVideoEventListeners();
      this.cleanEntities();
      this.lineArrVd = []; // 初始化路径数组
      this.uavDetailsVd = [];
      this.durationVd = 500;
      this.longitudeVd = '';//经度
      this.latitudeVd = '';//纬度
      this.heightVd = '';//高度
      this.elevationVd = '';//相对起飞点高度
      this.horizontalSpeedVd = '';//水平速度
      this.verticalSpeed = '';//垂直速度
      this.FJindex = 0;//拖动进度条记录索引
      this.isMapExpanded = false; // 新增状态变量
      
      // 销毁Cesium实例
      if (this.cesiumViewer) {
        this.cesiumViewer.destroy();
        this.cesiumViewer = null;
      }
      
      this.$emit('closeExit', false);
    }
  }
}
</script>

<style scoped>
.videoMap {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-container-small {
  position: absolute;
  top: 68%;
  left: 10px;
  width: 250px;
  height: 150px;
  z-index: 1;
}

.map-container {
  position: absolute;
  bottom: 80px;
  left: 10px;
  width: 250px;
  height: 150px;
}

.map-container-expanded {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.overlay {
  position: absolute;
  bottom: 80px;
  left: 10px;
  background-color: #ffffff;
  color: #ffffff;
}

.fly_param {
  position: absolute;
  width: 42%;
  height: 15%;
  bottom: 80px;
  right: 10px;
  background: rgba(0, 0, 0, .5);
  padding: 20px 0 0 10px;
  display: flex;
  flex-wrap: wrap;
  z-index: 2;
  font-size: 12px;
}

.row {
  width: 33%;
  height: 25%;
  color: #fff;
  position: relative;
  display: flex;
  align-items: center;
}

.flyParamIcon {
  width: 2%;
  height: 55%;
  background: #11cc80;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.flyParamTitle {
  margin-left: 6%;
  flex: 1;
  white-space: nowrap; /* 防止换行 */
}

.text {
  margin-left: 0; /* 调整间距 */
  flex: 3;
}

.wnConversion {
  width: 37px;
  height: 37px;
  border-radius: 50%;
  position: absolute;
  bottom: 29px;
  font-size: 18px;
  right: 12%;
  cursor: pointer;
  text-align: center;
}

.wnConversion:hover {
  background: rgba(0, 0, 0, .5);
}

.wnConversion1 {
  width: 37px;
  height: 37px;
  border-radius: 50%;
  position: absolute;
  top: 20%;
  font-size: 18px;
  right: 1%;
  cursor: pointer;
  text-align: center;
  background: rgba(0, 0, 0, .5);
  z-index: 1;
}

.speed {
  width: 37px;
  height: 37px;
  border-radius: 50%;
  position: absolute;
  bottom: 29px;
  right: 16%;
  cursor: pointer;
  display: flex; /* 使用 Flexbox 布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 12px;
  color: white; /* 可选：设置文本颜色 */
}

.speed:hover {
  background: rgba(0, 0, 0, .5);
}

.speed1 {
  width: 37px;
  height: 37px;
  border-radius: 50%;
  position: absolute;
  top: 26%;
  right: 1%;
  cursor: pointer;
  display: flex; /* 使用 Flexbox 布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 12px;
  color: white; /* 可选：设置文本颜色 */
  z-index: 1;
  background: rgba(0, 0, 0, .5);
}

.close {
  position: absolute;
  top: 0.5%;;
  right: 1%;
}

:deep(.cesium-viewer-bottom) {
  display: none;
}
</style>