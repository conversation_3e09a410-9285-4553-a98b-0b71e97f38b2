import { CURRENT_CONFIG } from '@/config/dijconfig'

export const AMapConfig = {
  key: CURRENT_CONFIG.amapKey,
  version: '2.0',
  plugins: [
    'AMap.Scale',
    'AMap.ToolBar',
    'AMap.ControlBar',
    'AMap.ElasticMarker',
    'AMap.MapType',
    'AMap.Geocoder',
    'AMap.CircleEditor',
    'AMap.PolygonEditor',
    'AMap.PolylineEditor',
    'AMap.PolyEditor',
    'AMap.RangingTool',
    'AMap.Weather',
    'AMap.MouseTool',
    'AMap.MoveAnimation'
  ]
}

export const CESIUM_CONFIG = {
  accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI5MTViZjRhZC0zNTJjLTQwZDAtODA1Ni0wNjIwMDAxNmZjNzYiLCJpZCI6MzAwMTk2LCJpYXQiOjE3NDY1OTg2MjF9.89rxA6kbhAHj0GRFsYjvWRKtMf-Qm5XPhKzEuEKmjT4', // 请替换为您的Cesium Ion访问令牌
  tdtKey: CURRENT_CONFIG.tdtKey
}
