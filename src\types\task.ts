import { commonColor } from '@/utils/color'

// 任务类型
export enum TaskType {
  Immediate = 'immediate', // 立即任务
  Timed = 'timed', // 单次定时任务
  Recurring = 'recurring', // 重复任务
  Continuous = 'continuous', // 连续任务
}

export const TaskTypeMap = {
  [TaskType.Immediate]: '立即任务',
  [TaskType.Timed]: '单次定时任务',
  [TaskType.Recurring]: '重复任务',
  [TaskType.Continuous]: '连续任务',
}

export const TaskTypeStringMap = {
  [TaskType.Immediate]: '立即任务',
  [TaskType.Timed]: '单次定时任务',
  [TaskType.Recurring]: '重复任务',
  [TaskType.Continuous]: '连续任务',
}

export const TaskTypeOptions = [
  { value: TaskType.Immediate, label: TaskTypeMap[TaskType.Immediate] },
  { value: TaskType.Timed, label: TaskTypeMap[TaskType.Timed] },
  { value: TaskType.Recurring, label: TaskTypeMap[TaskType.Recurring] },
  { value: TaskType.Continuous, label: TaskTypeMap[TaskType.Continuous] },
]

// 失控动作
export enum OutOfControlAction {
  ReturnToHome = 0,
  Hover = 1,
  Land = 2,
}

export const OutOfControlActionMap = {
  [OutOfControlAction.ReturnToHome]: '返航',
  [OutOfControlAction.Hover]: '悬停',
  [OutOfControlAction.Land]: '降落',
}

export const OutOfControlActionOptions = [
  { value: OutOfControlAction.ReturnToHome, label: OutOfControlActionMap[OutOfControlAction.ReturnToHome] },
  { value: OutOfControlAction.Hover, label: OutOfControlActionMap[OutOfControlAction.Hover] },
  { value: OutOfControlAction.Land, label: OutOfControlActionMap[OutOfControlAction.Land] },
]

// 任务状态
export enum TaskStatus {
  waiting = 1, // 待开始
  starting_failure = 2, // 启动失败
  executing = 3, // 执行中
  paused = 4, // 暂停
  terminated = 5, // 终止
  success = 6, // 成功
  suspended = 7, // 挂起
  timeout = 8, // 超时
}

export const TaskStatusMap = {
  [TaskStatus.waiting]: '待开始',
  [TaskStatus.starting_failure]: '启动失败',
  [TaskStatus.executing]: '执行中',
  [TaskStatus.paused]: '暂停',
  [TaskStatus.terminated]: '终止',
  [TaskStatus.success]: '成功',
  [TaskStatus.suspended]: '挂起',
  [TaskStatus.timeout]: '超时',
}

export const TaskStatusColor = {
  [TaskStatus.waiting]: commonColor.BLUE,
  [TaskStatus.starting_failure]: commonColor.FAIL,
  [TaskStatus.executing]: commonColor.BLUE,
  [TaskStatus.paused]: commonColor.BLUE,
  [TaskStatus.terminated]: commonColor.FAIL,
  [TaskStatus.success]: commonColor.NORMAL,
  [TaskStatus.suspended]: commonColor.BLUE,
  [TaskStatus.timeout]: commonColor.FAIL,
}

// 任务执行 ws 消息状态
export enum TaskProgressStatus {
  Sent = 'sent', // 已下发
  inProgress = 'in_progress', // 执行中
  Paused = 'paused', // 暂停
  Rejected = 'rejected', // 拒绝
  Canceled = 'canceled', // 取消或终止
  Timeout = 'timeout', // 超时
  Failed = 'failed', // 失败
  OK = 'ok', // 上传成功
}

// 任务进度消息
export interface TaskProgressInfo {
  bid: string,
  output:{
    ext: {
      current_waypoint_index: number,
      media_count: number // 媒体文件
    },
    progress:{
      current_step: number,
      percent: number
    },
    status: TaskProgressStatus
  },
  result: number,
}

// ws status => log status
export const TaskProgressWsStatusMap = {
  [TaskProgressStatus.Sent]: TaskStatus.executing,
  [TaskProgressStatus.inProgress]: TaskStatus.executing,
  [TaskProgressStatus.Rejected]: TaskStatus.starting_failure,
  [TaskProgressStatus.OK]: TaskStatus.success,
  [TaskProgressStatus.Failed]: TaskStatus.terminated,
  [TaskProgressStatus.Canceled]: TaskStatus.terminated,
  [TaskProgressStatus.Timeout]: TaskStatus.timeout,
  [TaskProgressStatus.Paused]: TaskStatus.paused,
}

// 根据媒体文件上传进度信息，前端自己判断出的状态
export enum MediaStatus { // 媒体上传进度
  ToUpload = 'to_upload', // 待上传
  Uploading = 'uploading', // 上传中
  Empty = 'empty', // 无媒体文件
  Success = 'success', // 上传成功
}

export const MediaStatusMap = {
  [MediaStatus.ToUpload]: '待上传',
  [MediaStatus.Uploading]: '上传中',
  [MediaStatus.Success]: '上传成功',
  [MediaStatus.Empty]: '无媒体文件',
}

export const MediaStatusColorMap = {
  [MediaStatus.ToUpload]: commonColor.BLUE,
  [MediaStatus.Uploading]: commonColor.BLUE,
  [MediaStatus.Success]: commonColor.NORMAL,
  [MediaStatus.Empty]: commonColor.WARN,
}

// 媒体上传进度消息
export interface MediaStatusProgressInfo {
  job_id: string,
  media_count: number
  uploaded_count: number,
}

// 媒体上传优先级消息
export interface TaskMediaHighestPriorityProgressInfo {
  pre_job_id: string,
  job_id: string,
}

// 航线类型
export enum WaylineType {
  Wayline = 0, // 航点航线
  Mapping = 1, // 建图航拍
  Oblique = 2, // 倾斜摄影
  // Flight = 3, // 航带航线
}

export const WaylineTypeMap = {
  [WaylineType.Wayline]: '航点航线',
  [WaylineType.Mapping]: '建图航拍',
  [WaylineType.Oblique]: '倾斜摄影',
  // [WaylineType.Flight]: '航带航线',
}
