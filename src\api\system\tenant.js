import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hztech-system/tenant/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = id => {
  return request({
    url: '/hztech-system/tenant/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

export const add = row => {
  return request({
    url: '/hztech-system/tenant/submit',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/hztech-system/tenant/submit',
    method: 'post',
    data: row,
  });
};

export const setting = (ids, form) => {
  return request({
    url: '/hztech-system/tenant/setting',
    method: 'post',
    params: {
      ...form,
      ids,
    },
  });
};

export const datasource = (tenantId, datasourceId) => {
  return request({
    url: '/hztech-system/tenant/datasource',
    method: 'post',
    params: {
      tenantId,
      datasourceId,
    },
  });
};

export const info = domain => {
  return request({
    url: '/hztech-system/tenant/info',
    method: 'get',
    params: {
      domain,
    },
  });
};

export const packageInfo = tenantId => {
  return request({
    url: '/hztech-system/tenant/package-detail',
    method: 'get',
    params: {
      tenantId,
    },
  });
};

export const packageSetting = (tenantId, packageId) => {
  return request({
    url: '/hztech-system/tenant/package-setting',
    method: 'post',
    params: {
      tenantId,
      packageId,
    },
  });
};

export const recycle = ids => {
  return request({
    url: '/hztech-system/tenant/recycle',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const pass = ids => {
  return request({
    url: '/hztech-system/tenant/pass',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/hztech-system/tenant/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const getUnBindList = (current, size, params) => {
  return request({
    url: '/hztech-sk2/tenantProject/unBindList',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getBindList = (current, size, params) => {
  return request({
    url: '/hztech-sk2/tenantProject/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const bind = (params) => {
  return request({
    url: '/hztech-sk2/tenantProject/Bind',
    method: 'get',
    params,
  });
};

export const unBind = (params) => {
  return request({
    url: '/hztech-sk2/tenantProject/unBind',
    method: 'get',
    params,
  });
};