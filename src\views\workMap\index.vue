<template>
    <div class="work-map-container">
        <left-sider />
        <div id="cockpitMap" class="map-view"></div>
        <el-button type="primary" class="take-off-btn" @click="takeOff">一键起飞</el-button>
    </div>
</template>

<script setup>
import LeftSider from '@/components/left-sider/main.vue';
import { useGMapManage } from '@/hooks/use-c-map';
import { onMounted, ref } from 'vue';
import { getApp } from "@/root";
import { getList, fly } from '@/api/device/index';
import centerIcon from '@/assets/center1.png';
import centerIcon2 from '@/assets/center2.png';
import { ElMessage } from 'element-plus';

// 存储选中的机场sn
const selectedDockSn = ref(null);

onMounted(async () => {
    const app = getApp();
    let mapCenter = [114.31, 30.57]; // 默认中心
    // 等待地图初始化完成
    await useGMapManage().globalPropertiesConfig(app, mapCenter, 'cockpitMap', 'Cesium');
    // 获取机场区域数据
    getDockArea();
});

const dockArea = ref([]);
const getDockArea = () => {
    getList(1, -1, {
        type: 'gateway',
    }).then(res => {
        dockArea.value = res.data.data.records;
        // 遍历数组，将有效坐标和范围添加到地图上
        addOperationRangesToMap();
    });
};

// 添加操作范围到地图上
const addOperationRangesToMap = () => {
    // 获取地图实例
    const app = getApp();
    const viewer = app.config.globalProperties.$cockpitMap || app.config.globalProperties.$map;
    
    if (!viewer) {
        console.error('地图实例未找到');
        return;
    }
    
    console.log('地图实例:', viewer);

    // 存储所有有效操作区域的坐标和范围
    const validAreas = [];

    // 设置鼠标交互
    setupMouseInteraction(viewer);

    dockArea.value.forEach(dock => {
        const { longitude, latitude, operationRangeKm } = dock;
        console.log(dock);

        // 检查坐标和范围是否有效
        if (longitude && latitude && operationRangeKm &&
            longitude !== -1 && latitude !== -1 && operationRangeKm !== -1) {

            // 保存有效区域数据
            validAreas.push({
                longitude: parseFloat(longitude),
                latitude: parseFloat(latitude),
                radius: parseFloat(operationRangeKm)
            });

            // 添加实体并保存其id
            const entity = viewer.entities.add({
                id: dock.sn,  // 使用机场的sn作为实体的id
                position: Cesium.Cartesian3.fromDegrees(parseFloat(longitude), parseFloat(latitude)),
                billboard: {
                    image: centerIcon,
                    scale: 0.4,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.CENTER
                },
                ellipse: {
                    semiMinorAxis: parseFloat(operationRangeKm) * 1000, // 转换为米
                    semiMajorAxis: parseFloat(operationRangeKm) * 1000, // 转换为米
                    material: Cesium.Color.LIGHTSKYBLUE.withAlpha(0.5), // 淡蓝色半透明
                    outline: true,
                    outlineColor: Cesium.Color.BLUE
                },
                // 存储机场数据供点击时使用
                properties: {
                    dockData: dock
                }
            });
        }
    });

    // 如果有有效区域，设置视角以显示所有区域
    if (validAreas.length > 0) {
        setViewToShowAllAreas(viewer, validAreas);
    }
};

// 设置鼠标交互
const setupMouseInteraction = (viewer) => {
    // 保存当前被选中的实体，用于在切换选中状态时恢复之前选中实体的样式
    let selectedEntity = null;
    
    // 设置鼠标悬停和点击事件处理
    const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
    
    // 鼠标移动事件
    handler.setInputAction((movement) => {
        const pickedObject = viewer.scene.pick(movement.endPosition);
        
        // 改变鼠标样式
        if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.billboard) {
            document.body.style.cursor = 'pointer'; // 设置鼠标为手型
        } else {
            document.body.style.cursor = 'default'; // 恢复默认鼠标样式
        }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    
    // 鼠标点击事件
    handler.setInputAction((click) => {
        const pickedObject = viewer.scene.pick(click.position);
        
        if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.billboard) {
            // 获取点击的实体
            const clickedEntity = pickedObject.id;
            
            // 如果有之前选中的实体，恢复其样式
            if (selectedEntity && selectedEntity !== clickedEntity) {
                selectedEntity.billboard.image = centerIcon;
            }
            
            // 更新当前选中的实体
            if (selectedEntity !== clickedEntity) {
                clickedEntity.billboard.image = centerIcon2; // 改变图标为选中状态
                selectedEntity = clickedEntity;
                // 存储选中的机场sn
                selectedDockSn.value = clickedEntity.id;
                console.log('选中的机场sn:', selectedDockSn.value);
            } else {
                // 如果点击的是已选中的实体，则取消选中
                clickedEntity.billboard.image = centerIcon;
                selectedEntity = null;
                selectedDockSn.value = null;
                console.log('取消选中');
            }
        }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

// 设置视角以显示所有操作区域
const setViewToShowAllAreas = (viewer, areas) => {
    // 计算边界矩形
    let west = 180;
    let south = 90;
    let east = -180;
    let north = -90;

    // 考虑每个区域的位置和半径来计算边界
    areas.forEach(area => {
        // 将圆的半径考虑进去，半径单位是千米，需要转为经纬度大致范围
        // 1度经度约等于地球赤道上111.32千米，1度纬度约为110.57千米
        const radiusLat = area.radius / 110.57; // 纬度方向上的度数范围
        const radiusLng = area.radius / (111.32 * Math.cos(area.latitude * Math.PI / 180)); // 经度方向上的度数范围
        
        west = Math.min(west, area.longitude - radiusLng);
        south = Math.min(south, area.latitude - radiusLat);
        east = Math.max(east, area.longitude + radiusLng);
        north = Math.max(north, area.latitude + radiusLat);
    });
    
    // 为了确保边界有一些填充空间，稍微扩大一点矩形
    const padding = 0.2; // 20%的填充
    const width = east - west;
    const height = north - south;
    
    const paddedWest = west - width * padding;
    const paddedSouth = south - height * padding;
    const paddedEast = east + width * padding;
    const paddedNorth = north + height * padding;
    
    // 创建矩形
    const rectangle = Cesium.Rectangle.fromDegrees(paddedWest, paddedSouth, paddedEast, paddedNorth);
    
    // 使用flyTo飞行到矩形区域上方
    viewer.camera.flyTo({
        destination: calculateCameraPositionForRectangle(viewer, rectangle),
        orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_TWO,  // 向下看
            roll: 0.0
        },
        duration: 1.5, // 飞行时间，单位秒
        complete: () => {
            console.log('相机已飞行到可以显示所有操作区域的位置');
        }
    });
};

// 计算查看矩形的相机位置
const calculateCameraPositionForRectangle = (viewer, rectangle) => {
    // 获取矩形中心点
    const center = Cesium.Rectangle.center(rectangle);
    const centerCartographic = Cesium.Cartographic.fromRadians(center.longitude, center.latitude);
    
    // 计算矩形的宽度和高度（以米为单位）
    const west = rectangle.west;
    const south = rectangle.south;
    const east = rectangle.east;
    const north = rectangle.north;
    
    const westCartographic = new Cesium.Cartographic(west, centerCartographic.latitude, 0);
    const eastCartographic = new Cesium.Cartographic(east, centerCartographic.latitude, 0);
    const southCartographic = new Cesium.Cartographic(centerCartographic.longitude, south, 0);
    const northCartographic = new Cesium.Cartographic(centerCartographic.longitude, north, 0);
    
    // 转换为世界坐标
    const ellipsoid = viewer.scene.globe.ellipsoid;
    const westCartesian = ellipsoid.cartographicToCartesian(westCartographic);
    const eastCartesian = ellipsoid.cartographicToCartesian(eastCartographic);
    const southCartesian = ellipsoid.cartographicToCartesian(southCartographic);
    const northCartesian = ellipsoid.cartographicToCartesian(northCartographic);
    
    // 计算矩形的对角线长度
    const widthMeters = Cesium.Cartesian3.distance(westCartesian, eastCartesian);
    const heightMeters = Cesium.Cartesian3.distance(southCartesian, northCartesian);
    
    // 选择较大的尺寸来决定相机高度
    const maxDimension = Math.max(widthMeters, heightMeters);
    
    // 设置相机高度，保证能看到整个矩形
    const cameraHeight = maxDimension * 1.5; // 乘以一个系数确保视野足够
    
    // 创建相机位置（中心点上方）
    centerCartographic.height = cameraHeight;
    return ellipsoid.cartographicToCartesian(centerCartographic);
};

const takeOff = () => {
    if (!selectedDockSn.value) {
        ElMessage.warning('请先选择一个机场');
        return;
    }
    
    fly({
        dockSn: selectedDockSn.value,
    }).then(res => {
        ElMessage.success('起飞指令已发送');
    }).catch(err => {
    });
};
</script>

<style lang="scss" scoped>
.work-map-container {
    display: flex;
    height: 100%;
}

:deep(.cesium-viewer-bottom) {
    display: none;
}

.take-off-btn {
    position: fixed;
    bottom: 5%;
    right: 50%;
    transform: translateX(50%);
    z-index: 1000;
}
</style>