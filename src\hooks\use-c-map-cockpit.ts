import store from '@/store'
import { getRoot } from '@/root'
import { ELocalStorageKey, EDeviceTypeName } from '@/types'
import { getDeviceBySn } from '@/api/manage'
import { message } from 'ant-design-vue'
import * as Cesium from 'cesium'
import dockIcon from '@/assets/dock.png'
import rcIcon from '@/assets/rc.png'
import droneIcon from '@/assets/m30.png'

export function cockpitTsaUpdate() {
  const root = getRoot()
  let defaultLng = 120.241193;
  let defaultLat = 30.299593;
  let defaultHeight = 0;
  const icons = new Map([
    [EDeviceTypeName.Aircraft, droneIcon],
    [EDeviceTypeName.Gateway, rcIcon],
    [EDeviceTypeName.Dock, dockIcon]
  ])

  let markers = {}
  const paths = {}
  const paths2 = {};
  const polyLines = {};
  const oldLines = {};
  let trackLine = null as any

  function getTrackLineInstance() {
    if (!trackLine) {
      trackLine = root.$cockpitMap.entities.add({
        polyline: {
          positions: [],
          width: 2,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.1,
            color: Cesium.Color.BLUE
          })
        }
      })
    }
    return trackLine
  }

  function newTrackLineInstance() {
    return root.$cockpitMap.entities.add({
      polyline: {
        positions: [],
        width: 4,
        material: new Cesium.ColorMaterialProperty(Cesium.Color.GREEN)
      }
    })
  }

  function initMarker(type: number, name: string, sn: string, lng?: number, lat?: number, height?: number) {
    if (markers[sn]) {
      return
    }
    if (!sn || !lng) {
      return
    }
    if (!root.$cockpitMap) {
      return [defaultLng, defaultLat, defaultHeight]
    }

    const position = Cesium.Cartesian3.fromDegrees(lng || defaultLng, lat || defaultLat, height || defaultHeight)
    const billboard = root.$cockpitMap.entities.add({
      name: name,
      position: position,
      billboard: {
        image: icons.get(type),
        width: 40,
        height: 40,
        // clampToGround: height ? false : true,
        // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })

    markers[sn] = billboard

    // 轨迹显示
    billboard.onMoving = function (e: any) {
      let modeCode = 0;
      if ((store.state.dock.deviceState.deviceInfo[sn] && store.state.dock.deviceState.deviceInfo[sn].mode_code)) {
        modeCode = store.state.dock.deviceState.deviceInfo[sn].mode_code;
      }

      if (modeCode && modeCode != 0) {
        let path = paths[sn]
        let path2 = paths2[sn];
        if (!path || !path2) {
          paths[sn] = [e.position];
          paths2[sn] = [e.position];
          return
        }
        if (polyLines[sn] && polyLines[sn].length > 5000) {
          for (let i = 0; i < polyLines[sn].length; i++) {
            root.$cockpitMap.entities.remove(polyLines[sn][i])
          }
          if (oldLines[sn]) {
            for (let i = 0; i < oldLines[sn].length; i++) {
              root.$cockpitMap.entities.remove(oldLines[sn][i])
            }
          }
          polyLines[sn] = [];
          oldLines[sn] = [];
          let timepolLine = oldLines[sn];
          let newLine = newTrackLineInstance();
          newLine.polyline.positions = path;
          timepolLine.push(newLine);
          paths[sn] = []
        }
        path.push(e.position);
        path2.push(e.position);

        if (path2.length > 5) {
          let line = newTrackLineInstance();
          line.polyline.positions = path2;
          if (!polyLines[sn]) {
            polyLines[sn] = []
          }
          let polyLine = polyLines[sn];
          polyLine.push(line)
          paths2[sn] = [e.position]
        }
      }
    }
  }

  function removeMarker(sn: string) {
    if (!markers[sn]) {
      return
    }
    root.$cockpitMap.entities.remove(markers[sn])
    if (polyLines[sn]) {
      for (let i = 0; i < polyLines[sn].length; i++) {
        root.$cockpitMap.entities.remove(polyLines[sn][i])
      }
    }

    if (oldLines[sn]) {
      for (let i = 0; i < oldLines[sn].length; i++) {
        root.$cockpitMap.entities.remove(oldLines[sn][i])
      }
    }
    delete polyLines[sn]
    delete oldLines[sn]
    delete markers[sn]
    delete paths[sn]
  }

  function removeAllMarker() {
    for (let sn in markers) {
      removeMarker(sn)
    }
  }

  async function addMarker(sn: string, lng?: number, lat?: number, height?: number) {
    if (markers[sn]) {
      return;
    }
    getDeviceBySn(localStorage.getItem(ELocalStorageKey.WorkspaceId)!, sn)
      .then(data => {
        if (data.code !== 0) {
          message.error(data.message)
          return
        }
        if (data.data.domain === EDeviceTypeName.Aircraft) {
          enableEntityFollow(sn)
        }
        initMarker(data.data.domain, data.data.nickname, sn, lng, lat, height)
      })
  }

  function moveTo(sn: string, data: { lng: number, lat: number, height?: number, attitude_head?: number, modeCode?: number }) {
    console.log('moveTo', sn, data.lng, data.lat, data.height, data.modeCode)
    let marker = markers[sn]
    if (!marker) {
      addMarker(sn, data.lng || defaultLng, data.lat || defaultLat, data.height || defaultHeight)
      return
    }

    const position = Cesium.Cartesian3.fromDegrees(data.lng || defaultLng, data.lat || defaultLat, data.height || defaultHeight)
    marker.position = position

    if (data.attitude_head !== undefined && marker.billboard) {
      // heading is clockwise from North. Billboard rotation is in radians, counter-clockwise.
      const heading = Cesium.Math.toRadians(data.attitude_head)
      marker.billboard.rotation = -heading
    }

    // 手动触发轨迹绘制
    if (marker.onMoving) {
      marker.onMoving({ position: position });
    }
  }

  // 启用实体跟随
  function enableEntityFollow(sn: string) {
    console.log('enableEntityFollow', sn)
    if (!root.$cockpitMap || !markers[sn]) return;

    // 设置相机跟随无人机实体
    root.$cockpitMap.trackedEntity = markers[sn];
  }

  // 禁用实体跟随
  function disableEntityFollow() {
    if (!root.$cockpitMap) return;

    // 取消跟随
    root.$cockpitMap.trackedEntity = undefined;
  }

  return {
    marker: markers,
    initMarker,
    removeMarker,
    removeAllMarker,
    moveTo,
    addMarker,
    enableEntityFollow,
    disableEntityFollow
  }
}

/**
 * 测试驾驶舱无人机轨迹显示功能
 * @param startLng 起始经度
 * @param startLat 起始纬度
 * @param startHeight 起始高度
 * @param pointsCount 测试点数量
 * @param interval 位置更新间隔(毫秒)
 */
export function testDroneTrackDisplay(startLng = 120.241193, startLat = 30.299593, startHeight = 100, pointsCount = 100, interval = 500) {
  // 初始化轨迹更新工具
  const cockpitTsaUpdateHook = cockpitTsaUpdate();

  // 创建测试用的无人机信息
  const testDroneSn = 'TEST_COCKPIT_DRONE_' + Date.now();
  console.log(`开始测试驾驶舱无人机 ${testDroneSn} 轨迹显示`);

  // 初始化无人机标记
  cockpitTsaUpdateHook.initMarker(
    EDeviceTypeName.Aircraft,
    'Test Cockpit Drone',
    testDroneSn,
    startLng,
    startLat,
    startHeight
  );

  // 生成测试航线坐标 (螺旋线轨迹)
  const testPoints = [];
  for (let i = 0; i < pointsCount; i++) {
    const radius = 0.0005 * (i / 3);
    const angle = i * (Math.PI / 15);
    const lng = startLng + radius * Math.cos(angle);
    const lat = startLat + radius * Math.sin(angle);
    // 高度随着轨迹变化，形成上升螺旋线
    const height = startHeight + i * 2;
    testPoints.push({ lng, lat, height });
  }

  // 模拟设备状态数据
  if (!store.state.dock.deviceState.deviceInfo) {
    store.state.dock.deviceState.deviceInfo = {};
  }

  // 初始化无人机数据
  store.state.dock.deviceState.deviceInfo[testDroneSn] = {
    mode_code: 1, // 飞行模式，非0才会显示轨迹
    longitude: startLng,
    latitude: startLat,
    height: startHeight
  };

  // 设置当前设备类型和SN
  store.state.dock.deviceState.currentType = EDeviceTypeName.Aircraft;
  store.state.dock.deviceState.currentSn = testDroneSn;

  // 执行位置更新
  let pointIndex = 0;

  console.log('开始模拟驾驶舱无人机轨迹...');
  console.log(`将生成${pointsCount}个点，每${interval}毫秒更新一次，预计将触发${Math.floor(pointsCount / 50)}次path2长度超过50的处理`);

  let intervalId = setInterval(() => {
    if (pointIndex >= testPoints.length) {
      clearInterval(intervalId);
      console.log('驾驶舱无人机轨迹测试完成');
      return;
    }

    const point = testPoints[pointIndex];

    // 更新store中的位置数据
    store.state.dock.deviceState.deviceInfo[testDroneSn].longitude = point.lng;
    store.state.dock.deviceState.deviceInfo[testDroneSn].latitude = point.lat;
    store.state.dock.deviceState.deviceInfo[testDroneSn].height = point.height;

    // 直接调用移动函数
    cockpitTsaUpdateHook.moveTo(testDroneSn, { lng: point.lng, lat: point.lat, height: point.height });

    // 每10个点记录一次日志，减少控制台输出
    if (pointIndex % 10 === 0) {
      console.log(`驾驶舱无人机位置更新: 点${pointIndex}/${pointsCount} [${point.lng}, ${point.lat}, ${point.height}]`);
    }

    pointIndex++;
  }, interval);

  // 返回控制函数
  return {
    stop: () => {
      clearInterval(intervalId);
      cockpitTsaUpdateHook.removeMarker(testDroneSn);
      console.log('驾驶舱测试已停止并清理');
    },
    // 添加暂停和继续的功能
    pause: () => {
      clearInterval(intervalId);
      console.log('驾驶舱测试已暂停，当前进度：', pointIndex, '/', pointsCount);
    },
    resume: () => {
      if (pointIndex < pointsCount) {
        intervalId = setInterval(() => {
          if (pointIndex >= testPoints.length) {
            clearInterval(intervalId);
            console.log('驾驶舱无人机轨迹测试完成');
            return;
          }

          const point = testPoints[pointIndex];
          cockpitTsaUpdateHook.moveTo(testDroneSn, { lng: point.lng, lat: point.lat, height: point.height });

          if (pointIndex % 10 === 0) {
            console.log(`驾驶舱无人机位置更新: 点${pointIndex}/${pointsCount} [${point.lng}, ${point.lat}, ${point.height}]`);
          }

          pointIndex++;
        }, interval);
        console.log('驾驶舱测试已恢复');
      }
    }
  };
}