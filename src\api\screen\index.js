import request from '@/axios';
import {ELocalStorageKey} from "@/types";
import { getWorkspaceId } from '@/utils/storage'

const PREFIX = '/hztech-flight-core/manage/api/v1'

// 设备统计
export const DevicesStat = () => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/devicesStat`,
    method: 'get',
  });
};

// 机场在线设备列表
export const DockOnlineDevices = () => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/flightRecordDeviceList`,
    method: 'get',
  });
};

// 飞行器在线设备列表
export const DroneOnlineDevices = () => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/droneOnlineDevices`,
    method: 'get',
  });
};

// 飞行统计
export const FlightStat = (params) => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/flightStat`,
    method: 'get',
    params: params,
  });
};

// 飞行记录
export const FlightRecord = (params) => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/flightRecord`,
    method: 'get',
    params: params,
  });
};

// 飞行设备筛选
export const FlightDeviceFilter = () => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/flightRecordDeviceList`,
    method: 'get',
  });
};

// 航线总览
export const WayLineRecord = (params) => {
  return request({
    url: `${PREFIX}/largescreen/${getWorkspaceId()}/wayLineRecord`,
    method: 'get',
    params: params,
  });
};