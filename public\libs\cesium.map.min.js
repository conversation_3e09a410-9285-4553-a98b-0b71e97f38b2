(()=>{var ae=Object.create;var J=Object.defineProperty;var oe=Object.getOwnPropertyDescriptor;var ne=Object.getOwnPropertyNames;var le=Object.getPrototypeOf,ce=Object.prototype.hasOwnProperty;var he=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports);var me=(n,e,t,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of ne(e))!ce.call(n,r)&&r!==t&&J(n,r,{get:()=>e[r],enumerable:!(i=oe(e,r))||i.enumerable});return n};var M=(n,e,t)=>(t=n!=null?ae(le(n)):{},me(e||!n||!n.__esModule?J(t,"default",{value:n,enumerable:!0}):t,n));var f=he((Me,k)=>{k.exports=Cesium});var h=M(f(),1);var w=52.35987755982988,g=3.141592653589793,X=6378245,Y=.006693421622965943,P=class{static BD09ToGCJ02(e,t){let i=+e-.0065,r=+t-.006,s=Math.sqrt(i*i+r*r)-2e-5*Math.sin(r*w),a=Math.atan2(r,i)-3e-6*Math.cos(i*w),o=s*Math.cos(a),l=s*Math.sin(a);return[o,l]}static GCJ02ToBD09(e,t){t=+t,e=+e;let i=Math.sqrt(e*e+t*t)+2e-5*Math.sin(t*w),r=Math.atan2(t,e)+3e-6*Math.cos(e*w),s=i*Math.cos(r)+.0065,a=i*Math.sin(r)+.006;return[s,a]}static WGS84ToGCJ02(e,t){if(t=+t,e=+e,this.out_of_china(e,t))return[e,t];{let i=this.delta(e,t);return[e+i[0],t+i[1]]}}static GCJ02ToWGS84(e,t){if(t=+t,e=+e,this.out_of_china(e,t))return[e,t];{let i=this.delta(e,t),r=e+i[0],s=t+i[1];return[e*2-r,t*2-s]}}static delta(e,t){let i=this.transformLng(e-105,t-35),r=this.transformLat(e-105,t-35),s=t/180*g,a=Math.sin(s);a=1-Y*a*a;let o=Math.sqrt(a);return i=i*180/(X/o*Math.cos(s)*g),r=r*180/(X*(1-Y)/(a*o)*g),[i,r]}static transformLng(e,t){t=+t,e=+e;let i=300+e+2*t+.1*e*e+.1*e*t+.1*Math.sqrt(Math.abs(e));return i+=(20*Math.sin(6*e*g)+20*Math.sin(2*e*g))*2/3,i+=(20*Math.sin(e*g)+40*Math.sin(e/3*g))*2/3,i+=(150*Math.sin(e/12*g)+300*Math.sin(e/30*g))*2/3,i}static transformLat(e,t){t=+t,e=+e;let i=-100+2*e+3*t+.2*t*t+.1*e*t+.2*Math.sqrt(Math.abs(e));return i+=(20*Math.sin(6*e*g)+20*Math.sin(2*e*g))*2/3,i+=(20*Math.sin(t*g)+40*Math.sin(t/3*g))*2/3,i+=(160*Math.sin(t/12*g)+320*Math.sin(t*g/30))*2/3,i}static out_of_china(e,t){return t=+t,e=+e,!(e>73.66&&e<135.05&&t>3.86&&t<53.55)}},x=P;var b=class extends h.WebMercatorTilingScheme{constructor(e){super(e);let t=new h.WebMercatorProjection;this._projection.project=function(i,r){return r=x.WGS84ToGCJ02(h.Math.toDegrees(i.longitude),h.Math.toDegrees(i.latitude)),r=t.project(new h.Cartographic(h.Math.toRadians(r[0]),h.Math.toRadians(r[1]))),new h.Cartesian2(r.x,r.y)},this._projection.unproject=function(i,r){let s=t.unproject(i);return r=x.GCJ02ToWGS84(h.Math.toDegrees(s.longitude),h.Math.toDegrees(s.latitude)),new h.Cartographic(h.Math.toRadians(r[0]),h.Math.toRadians(r[1]))}}},_=b;var N=M(f(),1),q={img:"//webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",elec:"//webrd{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}",cva:"//webst{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"},G=class extends N.UrlTemplateImageryProvider{constructor(e={}){e.url=e.url||[e.protocol||"",q[e.style]||q.elec].join(""),(!e.subdomains||!e.subdomains.length)&&(e.subdomains=["01","02","03","04"]),e.crs==="WGS84"&&(e.tilingScheme=new _),super(e)}},T=G;var y=M(f(),1);var c=M(f(),1);var ge=637099681e-2,V=[1289059486e-2,836237787e-2,5591021,348198983e-2,167804312e-2,0],L=[75,60,45,30,15,0],de=[[1410526172116255e-23,898305509648872e-20,-1.9939833816331,200.9824383106796,-187.2403703815547,91.6087516669843,-23.38765649603339,2.57121317296198,-.03801003308653,173379812e-1],[-7435856389565537e-24,8983055097726239e-21,-.78625201886289,96.32687599759846,-1.85204757529826,-59.36935905485877,47.40033549296737,-16.50741931063887,2.28786674699375,1026014486e-2],[-3030883460898826e-23,898305509983578e-20,.30071316287616,59.74293618442277,7.357984074871,-25.38371002664745,13.45380521110908,-3.29883767235584,.32710905363475,685681737e-2],[-1981981304930552e-23,8983055099779535e-21,.03278182852591,40.31678527705744,.65659298677277,-4.44255534477492,.85341911805263,.12923347998204,-.04625736007561,448277706e-2],[309191371068437e-23,8983055096812155e-21,6995724062e-14,23.10934304144901,-.00023663490511,-.6321817810242,-.00663494467273,.03430082397953,-.00466043876332,25551644e-1],[2890871144776878e-24,8983055095805407e-21,-3068298e-14,7.47137025468032,-353937994e-14,-.02145144861037,-1234426596e-14,.00010322952773,-323890364e-14,826088.5]],Z=[[-.0015702102444,111320.7020616939,0x60e374c3105a3,-0x24bb4115e2e164,0x5cc55543bb0ae8,-0x7ce070193f3784,0x5e7ca61ddf8150,-0x261a578d8b24d0,0x665d60f3742ca,82.5],[.0008277824516172526,111320.7020463578,6477955746671607e-7,-4082003173641316e-6,1077490566351142e-5,-1517187553151559e-5,1205306533862167e-5,-5124939663577472e-6,9133119359512032e-7,67.5],[.00337398766765,111320.7020202162,4481351045890365e-9,-2339375119931662e-8,7968221547186455e-8,-1159649932797253e-7,9723671115602145e-8,-4366194633752821e-8,8477230501135234e-9,52.5],[.00220636496208,111320.7020209128,51751.86112841131,3796837749470245e-9,992013.7397791013,-122195221711287e-8,1340652697009075e-9,-620943.6990984312,144416.9293806241,37.5],[-.0003441963504368392,111320.7020576856,278.2353980772752,2485758690035394e-9,6070.750963243378,54821.18345352118,9540.606633304236,-2710.55326746645,1405.483844121726,22.5],[-.0003218135878613132,111320.7020701615,.00369383431289,823725.6402795718,.46104986909093,2351.343141331292,1.58060784298199,8.77738589078284,.37238884252424,7.45]],j=class{constructor(){this.isWgs84=!1}getDistanceByMC(e,t){if(!e||!t||(e=this.convertMC2LL(e),!e))return 0;let i=this.toRadians(e.lng),r=this.toRadians(e.lat);if(t=this.convertMC2LL(t),!t)return 0;let s=this.toRadians(t.lng),a=this.toRadians(t.lat);return this.getDistance(i,s,r,a)}getDistanceByLL(e,t){if(!e||!t)return 0;e.lng=this.getLoop(e.lng,-180,180),e.lat=this.getRange(e.lat,-74,74),t.lng=this.getLoop(t.lng,-180,180),t.lat=this.getRange(t.lat,-74,74);let i=this.toRadians(e.lng),r=this.toRadians(e.lat),s=this.toRadians(t.lng),a=this.toRadians(t.lat);return this.getDistance(i,s,r,a)}convertMC2LL(e){if(!e)return{lng:0,lat:0};let t={};if(this.isWgs84){t.lng=e.lng/2003750834e-2*180;let s=e.lat/2003750834e-2*180;return t.lat=180/Math.PI*(2*Math.atan(Math.exp(s*Math.PI/180))-Math.PI/2),{lng:t.lng.toFixed(6),lat:t.lat.toFixed(6)}}let i={lng:Math.abs(e.lng),lat:Math.abs(e.lat)},r;for(let s=0;s<V.length;s++)if(i.lat>=V[s]){r=de[s];break}return t=this.convertor(e,r),{lng:t.lng.toFixed(6),lat:t.lat.toFixed(6)}}convertLL2MC(e){if(!e)return{lng:0,lat:0};if(e.lng>180||e.lng<-180||e.lat>90||e.lat<-90)return e;if(this.isWgs84){let s={},a=6378137;s.lng=e.lng*Math.PI/180*a;let o=e.lat*Math.PI/180;return s.lat=a/2*Math.log((1+Math.sin(o))/(1-Math.sin(o))),{lng:parseFloat(s.lng.toFixed(2)),lat:parseFloat(s.lat.toFixed(2))}}e.lng=this.getLoop(e.lng,-180,180),e.lat=this.getRange(e.lat,-74,74);let t={lng:e.lng,lat:e.lat},i;for(let s=0;s<L.length;s++)if(t.lat>=L[s]){i=Z[s];break}if(!i){for(let s=0;s<L.length;s++)if(t.lat<=-L[s]){i=Z[s];break}}let r=this.convertor(e,i);return{lng:parseFloat(r.lng.toFixed(2)),lat:parseFloat(r.lat.toFixed(2))}}convertor(e,t){if(!e||!t)return{lng:0,lat:0};let i=t[0]+t[1]*Math.abs(e.lng),r=Math.abs(e.lat)/t[9],s=t[2]+t[3]*r+t[4]*r*r+t[5]*r*r*r+t[6]*r*r*r*r+t[7]*r*r*r*r*r+t[8]*r*r*r*r*r*r;return i*=e.lng<0?-1:1,s*=e.lat<0?-1:1,{lng:i,lat:s}}getDistance(e,t,i,r){return ge*Math.acos(Math.sin(i)*Math.sin(r)+Math.cos(i)*Math.cos(r)*Math.cos(t-e))}toRadians(e){return Math.PI*e/180}toDegrees(e){return 180*e/Math.PI}getRange(e,t,i){return t!=null&&(e=Math.max(e,t)),i!=null&&(e=Math.min(e,i)),e}getLoop(e,t,i){for(;e>i;)e-=i-t;for(;e<t;)e+=i-t;return e}lngLatToMercator(e){return this.convertLL2MC(e)}lngLatToPoint(e){let t=this.convertLL2MC(e);return{x:t.lng,y:t.lat}}mercatorToLngLat(e){return this.convertMC2LL(e)}pointToLngLat(e){let t={lng:e.x,lat:e.y};return this.convertMC2LL(t)}pointToPixel(e,t,i,r){if(!e)return;e=this.lngLatToMercator(e);let s=this.getZoomUnits(t),a=Math.round((e.lng-i.lng)/s+r.width/2),o=Math.round((i.lat-e.lat)/s+r.height/2);return{x:a,y:o}}pixelToPoint(e,t,i,r){if(!e)return;let s=this.getZoomUnits(t),a=i.lng+s*(e.x-r.width/2),o=i.lat-s*(e.y-r.height/2),l={lng:a,lat:o};return this.mercatorToLngLat(l)}getZoomUnits(e){return Math.pow(2,18-e)}},H=j;var D=class extends c.WebMercatorTilingScheme{constructor(e){super(e);let t=new H;this._projection.project=function(i,r){return r=r||{},r=x.WGS84ToGCJ02(c.Math.toDegrees(i.longitude),c.Math.toDegrees(i.latitude)),r=x.GCJ02ToBD09(r[0],r[1]),r[0]=Math.min(r[0],180),r[0]=Math.max(r[0],-180),r[1]=Math.min(r[1],74.000022),r[1]=Math.max(r[1],-71.988531),r=t.lngLatToPoint({lng:r[0],lat:r[1]}),new c.Cartesian2(r.x,r.y)},this._projection.unproject=function(i,r){return r=r||{},r=t.mercatorToLngLat({lng:i.x,lat:i.y}),r=x.BD09ToGCJ02(r.lng,r.lat),r=x.GCJ02ToWGS84(r[0],r[1]),new c.Cartographic(c.Math.toRadians(r[0]),c.Math.toRadians(r[1]))},this.resolutions=e.resolutions||[]}tileXYToNativeRectangle(e,t,i,r){let s=this.resolutions[i],a=e*s,o=(e+1)*s,l=((t=-t)+1)*s,d=t*s;return(0,c.defined)(r)?(r.west=a,r.south=d,r.east=o,r.north=l,r):new c.Rectangle(a,d,o,l)}positionToTileXY(e,t,i){let r=this._rectangle;if(!c.Rectangle.contains(r,e))return;let a=this._projection.project(e);if(!(0,c.defined)(a))return;let o=this.resolutions[t],l=Math.floor(a.x/o),d=-Math.floor(a.y/o);return(0,c.defined)(i)?(i.x=l,i.y=d,i):new c.Cartesian2(l,d)}},K=D;var Q={img:"//shangetu{s}.map.bdimg.com/it/u=x={x};y={y};z={z};v=009;type=sate&fm=46",vec:"//online{s}.map.bdimg.com/tile/?qt=tile&x={x}&y={y}&z={z}&styles=sl&v=020",custom:"//api{s}.map.bdimg.com/customimage/tile?&x={x}&y={y}&z={z}&scale=1&customid={style}",traffic:"//its.map.baidu.com:8002/traffic/TrafficTileService?time={time}&label={labelStyle}&v=016&level={z}&x={x}&y={y}&scaler=2"},U=class extends y.UrlTemplateImageryProvider{constructor(e={}){if(e.url=e.url||[e.protocol||"",Q[e.style]||Q.custom].join(""),e.crs==="WGS84"){let t=[];for(let i=0;i<19;i++)t[i]=256*Math.pow(2,18-i);e.tilingScheme=new K({resolutions:t,rectangleSouthwestInMeters:new y.Cartesian2(-2003772637e-2,-1247410417e-2),rectangleNortheastInMeters:new y.Cartesian2(2003772637e-2,1247410417e-2)})}else e.tilingScheme=new y.WebMercatorTilingScheme({rectangleSouthwestInMeters:new y.Cartesian2(-33554054,-33746824),rectangleNortheastInMeters:new y.Cartesian2(33554054,33746824)});e.maximumLevel=18,super(e),this._rectangle=this._tilingScheme.rectangle,this._url=e.url,this._crs=e.crs||"BD09",this._style=e.style||"normal"}requestImage(e,t,i){let r=this._tilingScheme.getNumberOfXTilesAtLevel(i),s=this._tilingScheme.getNumberOfYTilesAtLevel(i),a=this._url.replace("{z}",i).replace("{s}",String(1)).replace("{style}",this._style);return this._crs==="WGS84"?a=a.replace("{x}",String(e)).replace("{y}",String(-t)):a=a.replace("{x}",String(e-r/2)).replace("{y}",String(s/2-t-1)),y.ImageryProvider.loadImage(this,a)}},z=U;var $=M(f(),1),ue="//tiles{s}.geovisearth.com/base/v1/{style}/{z}/{x}/{y}?format={format}&tmsIds=w&token={key}",W=class extends $.UrlTemplateImageryProvider{constructor(e={}){e.url=e.url||[e.protocol||"",ue.replace(/\{style\}/g,e.style||"vec").replace(/\{format\}/g,e.format||"png").replace(/\{key\}/g,e.key||"")].join(""),e.subdomains=e.subdomains||["1","2","3"],super(e)}},v=W;var te=M(f(),1);var ee={img:"https://gac-geo.googlecnapps.cn/maps/vt?lyrs=s&x={x}&y={y}&z={z}",elec:"https://gac-geo.googlecnapps.cn/maps/vt?lyrs=m&x={x}&y={y}&z={z}",cva:"https://gac-geo.googlecnapps.cn/maps/vt?lyrs=h&x={x}&y={y}&z={z}",ter:"https://gac-geo.googlecnapps.cn/maps/vt?lyrs=t@131,r&x={x}&y={y}&z={z}",img_cva:"https://gac-geo.googlecnapps.cn/maps/vt?lyrs=y&x={x}&y={y}&z={z}"},O=class extends te.UrlTemplateImageryProvider{constructor(e={}){e.url=e.url||[e.protocol||"",ee[e.style]||ee.elec].join(""),e.crs==="WGS84"&&(e.tilingScheme=new _),super(e)}},C=O;var re=M(f(),1),fe="//t{s}.tianditu.gov.cn/DataServer?T={style}_w&x={x}&y={y}&l={z}&tk={key}",A=class extends re.UrlTemplateImageryProvider{constructor(e={}){super({url:[e.protocol||"",fe.replace(/\{style\}/g,e.style||"vec").replace(/\{key\}/g,e.key||"")].join(""),subdomains:["0","1","2","3","4","5","6","7"],maximumLevel:18})}},p=A;var se=M(f(),1);var ie={img:"//p{s}.map.gtimg.com/sateTiles/{z}/{sx}/{sy}/{x}_{reverseY}.jpg?version=400",elec:"//rt{s}.map.gtimg.com/tile?z={z}&x={x}&y={reverseY}&styleid={style}&scene=0&version=347"},F=class extends se.UrlTemplateImageryProvider{constructor(e={}){let t=e.url||[e.protocol||"",ie[e.style]||ie.elec].join("");e.url=t.replace("{style}",e.style||"1"),(!e.subdomains||!e.subdomains.length)&&(e.subdomains=["0","1","2"]),e.style==="img"&&(e.customTags={sx:(i,r,s,a)=>r>>4,sy:(i,r,s,a)=>(1<<a)-1-s>>4}),e.crs==="WGS84"&&(e.tilingScheme=new _),super(e)}},S=F;var m=M(f(),1),B=class extends m.GeographicTilingScheme{constructor(e={}){super(e),this._origin=e.origin||[-180,90],this._zoomOffset=e.zoomOffset||0,this._tileSize=e.tileSize||256,this._resolutions=e.resolutions||[]}get zoomOffset(){return this._zoomOffset}tileXYToRectangle(e,t,i,r){if(!this._resolutions||!this._resolutions[i+this._zoomOffset])return m.Rectangle.MAX_VALUE;let s=this._resolutions[i+this._zoomOffset]*this._tileSize,a=m.Math.toRadians(this._origin[0]+e*s),o=m.Math.toRadians(this._origin[1]-(t+1)*s),l=m.Math.toRadians(this._origin[0]+(e+1)*s),d=m.Math.toRadians(this._origin[1]-t*s);return(0,m.defined)(r)?(r.west=a,r.south=o,r.east=l,r.north=d,r):new m.Rectangle(a,o,l,d)}positionToTileXY(e,t,i){if(!this._resolutions||!this._resolutions[t+this._zoomOffset])return new m.Cartesian2;let r=this._resolutions[t+this._zoomOffset]*this._tileSize,s=m.Math.toDegrees(e.longitude),a=m.Math.toDegrees(e.latitude),o=Math.floor((s-this._origin[0])/r),l=Math.floor((this._origin[1]-a)/r);return(0,m.defined)(i)?(i.x=o,i.y=l,i):new m.Cartesian2(Math.max(0,o),Math.max(0,l))}},I=B;var u=M(f(),1),E=class extends u.WebMercatorTilingScheme{constructor(e={}){super(e),this._origin=e.origin||[-200375083427892e-7,200375083427892e-7],this._zoomOffset=e.zoomOffset||0,this._tileSize=e.tileSize||256,this._resolutions=e.resolutions||[]}get zoomOffset(){return this._zoomOffset}tileXYToNativeRectangle(e,t,i,r){if(!this._resolutions||!this._resolutions[i+this._zoomOffset])return u.Rectangle.MAX_VALUE;if(e<0||t<0)return u.Rectangle.MAX_VALUE;let s=this._resolutions[i+this._zoomOffset]*this._tileSize,a=this._origin[0]+e*s,o=this._origin[1]-(t+1)*s,l=this._origin[0]+(e+1)*s,d=this._origin[1]-t*s;return(0,u.defined)(r)?(r.west=a,r.south=o,r.east=l,r.north=d,r):new u.Rectangle(a,o,l,d)}positionToTileXY(e,t,i){let r=this._rectangle;if(!u.Rectangle.contains(r,e))return;if(!this._resolutions||!this._resolutions[t+this._zoomOffset])return new u.Cartesian2;let s=this._resolutions[t+this._zoomOffset]*this._tileSize,o=this._projection.project(e),l=Math.floor((o.x-this._origin[0])/s),d=Math.floor((this._origin[1]-o.y)/s);return(0,u.defined)(i)?(i.x=l,i.y=d,i):new u.Cartesian2(Math.max(0,l),Math.max(0,d))}},R=E;window&&window.Cesium&&(Object.isFrozen(window.Cesium)?(window.AMapImageryProvider=T,window.BaiduImageryProvider=z,window.GeoVisImageryProvider=v,window.GoogleImageryProvider=C,window.TdtImageryProvider=p,window.TencentImageryProvider=S,window.CustomGeographicTilingScheme=I,window.CustomMercatorTilingScheme=R):(window.Cesium.AMapImageryProvider=T,window.Cesium.BaiduImageryProvider=z,window.Cesium.GeoVisImageryProvider=v,window.Cesium.GoogleImageryProvider=C,window.Cesium.TdtImageryProvider=p,window.Cesium.TencentImageryProvider=S,window.Cesium.CustomGeographicTilingScheme=I,window.Cesium.CustomMercatorTilingScheme=R));})();
