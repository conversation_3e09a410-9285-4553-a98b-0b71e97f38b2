<template>
    <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" v-model:page="page" ref="crud"
            @row-del="rowDel" v-model="form" :permission="permissionList" @row-update="rowUpdate" @row-save="rowSave"
            :before-open="beforeOpen" @search-change="searchChange" @search-reset="searchReset"
            @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
            @refresh-change="refreshChange" @on-load="onLoad">
            <template #menu-left="{ size, row, index }">
                <el-button v-if="data.length === 0" type="primary" @click="addBindWayline(row)">新增绑定</el-button>
            </template>
            <template #menu="{ size, row, index }">
                <el-button type="primary" text icon="el-icon-link" plain @click="unbindWayline(row)">解除绑定</el-button>
            </template>
        </avue-crud>

        <!-- 添加弹窗 -->
        <el-dialog v-model="dialogVisible" destroy-on-close title="选择航线" width="70%">
            <wayline-list :dockSn="dockSn" ref="waylineList"></wayline-list>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmSelect">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </basic-container>
</template>

<script>
import { getDockWayline as getList, saveDockWayline, removeDockWayline } from '@/api/device/index';
import { mapGetters } from 'vuex';
// 引入 WaylineList 组件
import WaylineList from './WaylineList.vue';

export default {
    components: {
        WaylineList
    },
    props: {
        dockSn: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 32,
                dialogWidth: 950,
                tip: false,
                searchShow: true,
                searchMenuSpan: 6,
                border: true,
                index: false,
                selection: false,
                excelBtn: true,
                delBtn: false,
                addBtn: false,
                editBtn: false,
                dialogClickModal: false,
                grid: false,
                labelWidth: 120,
                column: [
                    {
                        label: '机场',
                        prop: 'dockSn',
                        display: false
                    },
                    {
                        label: '航线',
                        prop: 'waylineName',
                        display: false,
                    },
                    {
                        label: '返航高度',
                        prop: 'returnHeight',
                        display: false,
                    },
                ],
            },
            data: [],
            // 添加弹窗控制变量
            dialogVisible: false,
            currentRow: null,
        };
    },
    computed: {
        ...mapGetters(['permission']),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.notice_add, false),
                viewBtn: this.validData(this.permission.notice_view, false),
                delBtn: this.validData(this.permission.notice_delete, false),
                editBtn: this.validData(this.permission.notice_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(',');
        },
    },
    methods: {
        addBindWayline(row) {
            this.currentRow = row;
            this.dialogVisible = true;
        },
        // 确认选择航线
        confirmSelect() {
            const selectedWayline = this.$refs.waylineList.getSelectedRow();
            if (selectedWayline && this.$refs.waylineList.returnHeight) {
                console.log('选中的航线:', selectedWayline);
                saveDockWayline({
                    dockSn: this.dockSn,
                    waylineUuid: selectedWayline.id,
                    returnHeight: this.$refs.waylineList.returnHeight,
                }).then(res => {
                    this.$message.success('成功绑定航线');
                }).finally(() => {
                    setTimeout(() => {
                        this.onLoad(this.page);
                    }, 1000);
                });
                this.dialogVisible = false;
                // 刷新列表
            } else {
                this.$message.warning('请选择一条航线');
            }
        },
        unbindWayline(row) {
            removeDockWayline({
                ids: row.id,
            }).then(res => {
                this.$message.success('成功解除绑定');
                this.onLoad(this.page);
            });
        },
        rowSave(row, done, loading) {
            add(row).then(
                () => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    done();
                },
                error => {
                    window.console.log(error);
                    loading();
                }
            );
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(
                () => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    done();
                },
                error => {
                    window.console.log(error);
                    loading();
                }
            );
        },
        rowDel(row) {
            this.$confirm('确定将选择数据删除?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                });
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning('请选择至少一条数据');
                return;
            }
            this.$confirm('确定将选择数据删除?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (['edit', 'view'].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            let values = {
                dockSn: this.dockSn,
                ...params,
                ...this.query,
            };
            this.loading = true;
            getList(page.currentPage, page.pageSize, values).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>