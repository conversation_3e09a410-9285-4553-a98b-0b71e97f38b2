import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hztech-sk2/tenantDevice/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/hztech-sk2/tenantDevice/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const add = row => {
  return request({
    url: '/hztech-sk2/tenantDevice/submit',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/hztech-sk2/tenantDevice/submit',
    method: 'post',
    data: row,
  });
};

export const getDetail = id => {
  return request({
    url: '/hztech-sk2/tenantDevice/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

export const getDockWayline = (current, size, params) => {
  return request({
    url: '/hztech-sk2/dockWayline/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const saveDockWayline = (row) => {
  return request({
    url: '/hztech-sk2/dockWayline/save',
    method: 'post',
    data: row,
  });
};

export const removeDockWayline = (row) => {
  return request({
    url: '/hztech-sk2/dockWayline/remove',
    method: 'post',
    params: row,
  });
};

export const fly = (row) => {
  return request({
    url: '/hztech-sk2/flightTask/fly',
    method: 'post',
    data: row,
  });
};
