import request from '@/axios'
import { DeviceCmd, DeviceCmdItemAction } from '@/types/device-cmd'
export interface IResult {
  code: number;
  message: string;
 }
 
 export interface IPage {
  page: number;
  total: number;
  page_size: number;
 }
 
 export interface IListWorkspaceResponse<T> {
  code: number;
  message: string;
  data: {
    list: T[];
    pagination: IPage;
  };
 }
 // Workspace
 export interface IWorkspaceResponse<T> {
  code: number;
  data: T;
  message: string;
 }
 
 export type IStatus = 'WAITING' | 'DOING' | 'SUCCESS' | 'FAILED';
 
 export interface CommonListResponse<T> extends IResult {
  data: {
    list: T[];
    pagination: IPage;
  };
 }
 
 export interface CommonResponse<T> extends IResult {
  data: T
 }
 
const CMD_API_PREFIX = '/hztech-flight-core/control/api/v1'

export interface SendCmdParams {
  dock_sn: string, // 机场cn
  device_cmd: DeviceCmd // 指令
}

export interface PostSendCmdBody {
  action: DeviceCmdItemAction
}
/**
 * 发送机场控制指令
 * @param params
 * @returns
 */
// /control/api/v1/devices/{dock_sn}/jobs/{service_identifier}
export async function postSendCmd (params: SendCmdParams, body?: PostSendCmdBody): Promise<IWorkspaceResponse<{}>> {
  const resp = await request.post(`${CMD_API_PREFIX}/devices/${params.dock_sn}/jobs/${params.device_cmd}`, body)
  return resp.data
}
