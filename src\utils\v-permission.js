// 隐藏按钮
// export default {
//     beforeMount(el) {
//         el._permission = { style: el.style.display }; // 缓存原始状态
//         el.style.display = "none";
//     },
//     mounted: handlePermission,
//     updated: handlePermission
// }

// function hasPermission() {
//     try {
//         const userInfo = JSON.parse(localStorage.getItem('hztech-userInfo') || '{}');
//         return userInfo?.content?.role_name === 'ViewUers';
//     } catch (error) {
//         console.error('权限解析失败:', error);
//         return false;
//     }
// }

// function handlePermission(el) {
//     try {
//         const has = hasPermission();
//         el.style.display = has ? 'none' : el._permission.style; // 恢复原始状态或隐藏
//         // 若坚持要物理移除（需考虑Vue虚拟DOM一致性风险）
//         // if (has && el.parentNode) el.parentNode.removeChild(el);
//     } catch (error) {
//         console.error('权限控制异常:', error);
//         el.style.display = el._permission.style; // 出错时恢复原始状态
//     }
// }


// 禁用按钮
export default {
    beforeMount(el) {
        // 保存原始点击事件和光标样式
        el._permission = {
            onClick: el.onclick,
            cursor: el.style.cursor
        };
        
        // 添加权限拦截
        el.addEventListener('click', el._permission.interceptHandler = (e) => {
            if (!hasPermission()) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
            }
        }, true);

        updatePermission(el);
    },
    mounted(el) {
        updatePermission(el);
    },
    updated(el) {
        updatePermission(el);
    },
    beforeUnmount(el) {
        if (el._permission) {
            el.removeEventListener('click', el._permission.interceptHandler, true);
        }
    }
}

function hasPermission() {
    try {
        const userInfo = JSON.parse(localStorage.getItem('hztech-userInfo') || '{}');
        return userInfo?.content?.role_name !== 'ViewUers';
    } catch (error) {
        console.error('权限解析失败:', error);
        return false;
    }
}

function updatePermission(el) {
    const has = hasPermission();
    el.style.pointerEvents = has ? '' : 'none';
    el.style.opacity = has ? '' : '0.6';
    el.style.cursor = has ? el._permission.cursor : 'not-allowed';
}