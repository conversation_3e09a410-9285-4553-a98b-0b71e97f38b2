<template>
<div class="project-app-wrapper" :class="themeNameType?'sikongD':'sikong'" v-show="validSidebar">
	<div class="left">
		<LeftSider/>
		<div class="main-content uranus-scrollbar dark" >
		  <router-view/>
		</div>
	</div>
	<!-- <div v-if="root.$route.name === ERouterName.WAYLINE" class="lefts" >
		<Sidebar/>
	</div> -->
	<div class="right">
		<div class="map-wrapper">
		  <djimap />
		</div>
	</div>
</div>
    
  </template>
  <script lang="ts" setup>
  import LeftSider from '@/components/left-sider/main.vue'
  import djimap from '../../components/djimap/djimap.vue'

  import { EBizCode, ERouterName } from '@/types'
  import { getRoot } from '@/root'
  import store from '@/store'
  import { useConnectWebSocket } from '@/hooks/use-connect-websocket'
  import { useAirsenseWarning } from '@/hooks/use-airsense-warning'
  import EventBus from '@/event-bus'
  import { setTheme } from '@/utils/util';
  import topTheme from '@/page/index/top/top-theme.vue';
  import { computed, ref } from 'vue';
  
  const root = getRoot()
  // const AMap = root.$aMap
  const isCollapse = computed(() => store.state.common.isCollapse)
  const themeName = computed(() => store.state.common.themeName)
  
  const validSidebar = computed(() => {
    return !(
      (root.$route.meta || {}).menu === false || (root.$route.query || {}).menu === 'false'
    );
  });
  const themeNameType=ref(false)
  if(themeName.value=='default'){
	  themeNameType.value=true;
  }else{
	  themeNameType.value=false;
		setTheme(themeName.value);  
  }
  
  const messageHandler = async (payload: any) => {
    if (!payload) {
      return
    }
  
    switch (payload.biz_code) {
      case EBizCode.GatewayOsd: {
        store.commit('SET_GATEWAY_INFO', payload.data)
        break
      }
      case EBizCode.DeviceOsd: {
        store.commit('SET_DEVICE_INFO', payload.data)
        break
      }
      case EBizCode.DockOsd: {
        store.commit('SET_DOCK_INFO', payload.data)
        break
      }
      case EBizCode.MapElementCreate: {
        store.commit('SET_MAP_ELEMENT_CREATE', payload.data)
        break
      }
      case EBizCode.MapElementUpdate: {
        store.commit('SET_MAP_ELEMENT_UPDATE', payload.data)
        break
      }
      case EBizCode.MapElementDelete: {
        store.commit('SET_MAP_ELEMENT_DELETE', payload.data)
        break
      }
      case EBizCode.DeviceOnline: {
        store.commit('SET_DEVICE_ONLINE', payload.data)
        break
      }
      case EBizCode.DeviceOffline: {
        store.commit('SET_DEVICE_OFFLINE', payload.data)
        break
      }
      case EBizCode.FlightTaskProgress:
      case EBizCode.FlightTaskMediaProgress:
      case EBizCode.FlightTaskMediaHighestPriority: {
        EventBus.emit('flightTaskWs', payload)
        break
      }
      case EBizCode.DeviceHms: {
        store.commit('SET_DEVICE_HMS_INFO', payload.data)
        break
      }
      case EBizCode.DeviceReboot:
      case EBizCode.DroneOpen:
      case EBizCode.DroneClose:
      case EBizCode.CoverOpen:
      case EBizCode.CoverClose:
      case EBizCode.PutterOpen:
      case EBizCode.PutterClose:
      case EBizCode.ChargeOpen:
      case EBizCode.ChargeClose:
      case EBizCode.DeviceFormat:
      case EBizCode.DroneFormat:
      {
        store.commit('SET_DEVICES_CMD_EXECUTE_INFO', {
          biz_code: payload.biz_code,
          timestamp: payload.timestamp,
          ...payload.data,
        })
        break
      }
      case EBizCode.ControlSourceChange:
      case EBizCode.FlyToPointProgress:
      case EBizCode.TakeoffToPointProgress:
      case EBizCode.JoystickInvalidNotify:
      case EBizCode.DrcStatusNotify:
      case EBizCode.CloudControlAuthNotify:
      {
        EventBus.emit('droneControlWs', payload)
        break
      }
      case EBizCode.FlightAreasSyncProgress: {
        EventBus.emit('flightAreasSyncProgressWs', payload.data)
        break
      }
      case EBizCode.FlightAreasDroneLocation: {
        EventBus.emit('flightAreasDroneLocationWs', payload)
        break
      }
      // case EBizCode.AirsenseWarning: {
      //   EventBus.emit('airsenseWarningWs', payload)
      //   break
      // }
      case EBizCode.FlightAreasUpdate: {
        EventBus.emit('flightAreasUpdateWs', payload.data)
        break
      }
      // 红外测温变化
      case EBizCode.IrMeteringPoint: 
      case EBizCode.IrMeteringArea: 
      {
        console.log('irMeteringWs',payload.data);
        EventBus.emit('irMeteringWs', payload.data)
        break
      }
      case EBizCode.SpeakerPlayStartProgress: {
        EventBus.emit('speaker_play_start_progress_notify', payload.data)
        break
      }
      default:
        break
    }
  }
  
  // 监听ws 消息
  useConnectWebSocket(messageHandler)
  useAirsenseWarning()
  
  </script>
  <style lang="scss" scoped>
 @import '@/styles/dij/index.scss';
	.sikongD{
		background: #232323
	}
  
  .project-app-wrapper {
    display: flex;
    transition: width 0.2s ease;
    height: 100%;
    width: 100%;
  
    .left {
      display: flex;
      width: 335px;
      flex: 0 0 335px;
      // background-color: #232323;
  
      .main-content {
        flex: 1;
        // color: $text-white-basic;
        color: #000;
        width: 285px;
      }
    }
    .lefts {
      display: flex;
      background-color: #232323;
    }
    .right {
      flex-grow: 1;
      position: relative;
  
      .map-wrapper{
        width: 100%;
        height: 100%;
      }
  
      .media-wrapper,
      .task-wrapper {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: #f6f8fa;
      }
    }
  }
  </style>
  