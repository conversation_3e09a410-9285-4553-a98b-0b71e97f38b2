import Store from '@/store/';
import { ERouterName } from '@/api/enum';
import Layout from '@/views/workspace/Layout.vue';

export default [
  {
    path: '/login',
    name: '登录页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/oauth/redirect/:source',
    name: '第三方登录',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/lock',
    name: '锁屏页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/lock.vue') : import('@/page/lock/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/404.vue'),
    name: '404',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/403',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/403.vue'),
    name: '403',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/500',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/500.vue'),
    name: '500',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/',
    name: '主页',
    redirect: '/wel',
  },
  {
    path: '/workspace/index',
    name: '项目列表',
    component: () => import('@/views/workspace/index.vue'),
  },
  {
    path: '/waylineEditor/index',
    name: '编辑航线',
    meta: {
      isTab: false,
    },
    component: () => import('@/views/waylineEditor/index.vue'),
  },
  {
    path: '/project/index',
    name: '项目',
    meta: {
      isTab: false,
    },
    component: () => import('@/views/project/index.vue'),
  },
  {
    path: '/cockpit/index',
    name: '驾驶舱',
    meta: {
      isTab: false,
    },
    component: () => import('@/views/cockpit/index.vue'),
  },
  {
    path: '/device/index',
    name: '设备列表',
    meta: {
      isTab: false,
    },
    component: () => import('@/views/device/index.vue'),
  },
  {
    path: '/workMap/index',
    name: '作业地图',
    meta: {
      isTab: false,
    },
    component: () => import('@/views/workMap/index.vue'),
  },
  {
    path: '/workspace',
    name: '工作空间',
    meta: {
      isTab: false,
    },
    component: Layout,
    redirect: '/workspace/wayline',
    children: [
      {
        path: 'wayline',
        name: '航线库',
        component: () => import('@/views/wayline/index.vue'),
      },
      {
        path: 'task',
        name: '任务列表',
        component: () => import('@/views/task/index.vue'),
      },
    ],
  },
];

