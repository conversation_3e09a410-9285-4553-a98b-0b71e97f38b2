import store from '@/store'
import {getRoot} from '@/root'
import {ELocalS<PERSON>ageKey, EDeviceTypeName} from '@/types'
import {getDeviceBySn} from '@/api/manage'
import {message} from 'ant-design-vue'
import * as Cesium from 'cesium'
import { getCurrentInstance } from 'vue'
import dockIcon from '@/assets/dock.png'
import rcIcon from '@/assets/rc.png'
import droneIcon from '@/assets/m30.png'

export function deviceTsaUpdate() {
  const root = getRoot()
  const instance = getCurrentInstance()
  let defaultLng = 120.241193
  let defaultLat = 30.299593
  let defaultHeight = 0
  const icons = new Map([
    [EDeviceTypeName.Aircraft, droneIcon],
    [EDeviceTypeName.Gateway, rcIcon],
    [EDeviceTypeName.Dock, dockIcon]
  ])
  const markers = store.state.dock.markerInfo.coverMap
  const paths = store.state.dock.markerInfo.pathMap
  const paths2 = {}
  const polyLines = {}
  const oldLines = {}
  let trackLine = null as any

  function getTrackLineInstance() {
    if (!trackLine) {
      trackLine = root.$viewer.entities.add({
        polyline: {
          positions: [],
          width: 2,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.1,
            color: Cesium.Color.BLUE
          })
        }
      })
    }
    return trackLine
  }

  function newTrackLineInstance() {
    return root.$viewer.entities.add({
      polyline: {
        positions: [],
        width: 4,
        material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#0aed8b'))
      }
    })
  }

  function initMarker(type: number, name: string, sn: string, lng?: number, lat?: number, height?: number) {
   
    if (markers[sn]) {
      return
    }
    if (!sn || !lng) {
      return
    }
    if (!root.$viewer) {
      return [defaultLng, defaultLat, defaultHeight]
    }

    const position = Cesium.Cartesian3.fromDegrees(lng || defaultLng, lat || defaultLat, height || defaultHeight)
    console.log(lng || defaultLng, lat || defaultLat, height || defaultHeight);
    
    const billboard = root.$viewer.entities.add({
      name: name,
      position: position,
      billboard: {
        image: icons.get(type),
        width: 40,
        height: 40,
        // clampToGround: height ? true : false,
        // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })

    markers[sn] = billboard

    // 轨迹显示
    billboard.onMoving = function(e: any) {
      // console.log('onMoving triggered', sn, e.position);
      let modeCode = 0
      if (store.state.dock.deviceState.deviceInfo[sn] && store.state.dock.deviceState.deviceInfo[sn].mode_code) {
        modeCode = store.state.dock.deviceState.deviceInfo[sn].mode_code
      }

      if (modeCode && modeCode != 0) {
        let path = paths[sn]
        let path2 = paths2[sn]
        if (!path || !path2) {
          paths[sn] = [e.position]
          paths2[sn] = [e.position]
          return
        }

        if (polyLines[sn] && polyLines[sn].length > 5000) {
          for (let i = 0; i < polyLines[sn].length; i++) {
            root.$viewer.entities.remove(polyLines[sn][i])
          }
          if (oldLines[sn]) {
            for (let i = 0; i < oldLines[sn].length; i++) {
              root.$viewer.entities.remove(oldLines[sn][i])
            }
          }
          polyLines[sn] = []
          oldLines[sn] = []
          let timePolyline = oldLines[sn]
          let newLine = newTrackLineInstance()
          newLine.polyline.positions = path
          timePolyline.push(newLine)
          paths[sn] = []
        }

        path.push(e.position)
        path2.push(e.position)

        if (path2.length > 5) {
          let line = newTrackLineInstance()
          line.polyline.positions = path2
          if (!polyLines[sn]) {
            polyLines[sn] = []
          }
          let polyline = polyLines[sn]
          polyline.push(line)
          paths2[sn] = [e.position]
        }
      }
    }
  }

  function removeMarker(sn: string) {
    if (!markers[sn]) {
      return
    }
    root.$viewer.entities.remove(markers[sn])
    if (polyLines[sn]) {
      for (let i = 0; i < polyLines[sn].length; i++) {
        root.$viewer.entities.remove(polyLines[sn][i])
      }
    }
    if (oldLines[sn]) {
      for (let i = 0; i < oldLines[sn].length; i++) {
        root.$viewer.entities.remove(oldLines[sn][i])
      }
    }
    delete polyLines[sn]
    delete oldLines[sn]
    delete markers[sn]
    delete paths[sn]
  }

  function removeAllMarker() {
    // 遍历所有标记并从viewer中移除
    Object.keys(markers).forEach(sn => {
      root.$viewer.entities.remove(markers[sn])
      
      // 清理轨迹线
      if (polyLines[sn]) {
        for (let i = 0; i < polyLines[sn].length; i++) {
          root.$viewer.entities.remove(polyLines[sn][i])
        }
      }
      
      if (oldLines[sn]) {
        for (let i = 0; i < oldLines[sn].length; i++) {
          root.$viewer.entities.remove(oldLines[sn][i])
        }
      }
    })
    
    // 重置所有数据结构
    store.state.dock.markerInfo.coverMap = {}
    Object.keys(markers).forEach(key => delete markers[key])
    Object.keys(polyLines).forEach(key => delete polyLines[key])
    Object.keys(oldLines).forEach(key => delete oldLines[key])
    Object.keys(paths).forEach(key => delete paths[key])
    Object.keys(paths2).forEach(key => delete paths2[key])
    
    // 如果有跟踪实体，禁用跟随
    disableEntityFollow()
  }

  function addMarker(sn: string, lng?: number, lat?: number, height?: number) {
    getDeviceBySn(localStorage.getItem(ELocalStorageKey.WorkspaceId)!, sn)
      .then(data => {
        if (data.code !== 0) {
          message.error(data.message)
          return
        }
        initMarker(data.data.domain, data.data.nickname, sn, lng, lat, height)
      })
  }

  function moveTo(sn: string, lng: number, lat: number, height?: number, modeCode?: number) {
    console.log('moveTo', sn, lng, lat, height, modeCode);
    
    let marker = markers[sn]
    if (!marker) {
      addMarker(sn, lng || defaultLng, lat || defaultLat, height || defaultHeight)
      return
    }

    const position = Cesium.Cartesian3.fromDegrees(lng || defaultLng, lat || defaultLat, height || defaultHeight)
    marker.position = position
    
    // 手动触发轨迹绘制
    if (marker.onMoving) {
      marker.onMoving({ position: position });
    }

    // 如果是当前选中的设备并且当前设备类型是无人机，则启用相机跟随
    // if (store.state.dock.deviceState.currentSn === sn && 
    //     store.state.dock.deviceState.currentType === EDeviceTypeName.Aircraft) {
    //   // 检查是否启用了跟随模式
    //   const isFollowing = store.state.dock.cameraFollow || false;
    //   if (isFollowing) {
    //     enableEntityFollow(marker);
    //   }
    // }
  }

  // 启用实体跟随
  function enableEntityFollow(entity) {
    if (!root.$viewer || !entity) return;
    
    // 设置相机跟随无人机实体
    root.$viewer.trackedEntity = entity;
  }

  // 禁用实体跟随
  function disableEntityFollow() {
    if (!root.$viewer) return;
    
    // 取消跟随
    root.$viewer.trackedEntity = undefined;
  }

  // 获取指定SN的marker实体
  function getMarkerBySn(sn: string) {
    return markers[sn];
  }

  // 切换跟随状态
  function toggleFollow(sn?: string) {
    const isFollowing = !store.state.dock.cameraFollow;
    store.commit('SET_CAMERA_FOLLOW', isFollowing);
    
    if (isFollowing) {
      if (sn && markers[sn]) {
        enableEntityFollow(markers[sn]);
      }else{
        disableEntityFollow();
      }
    } else {
      disableEntityFollow();
    }
  }

  return {
    marker: markers,
    initMarker,
    removeMarker,
    removeAllMarker,
    moveTo,
    addMarker,
    enableEntityFollow,
    disableEntityFollow,
    toggleFollow,
    getMarkerBySn
  }
}

/**
 * 测试无人机轨迹显示功能
 * @param startLng 起始经度
 * @param startLat 起始纬度
 * @param startHeight 起始高度
 * @param pointsCount 测试点数量
 * @param interval 位置更新间隔(毫秒)
 */
export function testDroneTrackDisplay(startLng = 120.241193, startLat = 30.299593, startHeight = 100, pointsCount = 100, interval = 100) {
  // 初始化轨迹更新工具
  const deviceTsaUpdateHook = deviceTsaUpdate();
  
  // 创建测试用的无人机信息
  const testDroneSn = 'TEST_DRONE_' + Date.now();
  console.log(`开始测试无人机 ${testDroneSn} 轨迹显示`);
  
  // 初始化无人机标记
  deviceTsaUpdateHook.initMarker(
    EDeviceTypeName.Aircraft,
    'Test Drone',
    testDroneSn,
    startLng,
    startLat,
    startHeight
  );
  
  // 生成测试航线坐标（更紧凑的螺旋线以实现更平滑的移动效果）
  const testPoints = [];
  for (let i = 0; i < pointsCount; i++) {
    // 减小半径增长率，使点位更加紧凑
    const radius = 0.0002 * (i / 5);
    // 减小角度增量，使相邻点之间的距离更小
    const angle = i * (Math.PI / 60);
    const lng = startLng + radius * Math.cos(angle);
    const lat = startLat + radius * Math.sin(angle);
    // 高度变化更平缓
    const height = startHeight + i * 0.5;
    testPoints.push({ lng, lat, height });
  }
  
  // 模拟设备状态数据
  if (!store.state.dock.deviceState.deviceInfo) {
    store.state.dock.deviceState.deviceInfo = {};
  }
  
  // 初始化无人机数据
  store.state.dock.deviceState.deviceInfo[testDroneSn] = {
    mode_code: 1, // 飞行模式，非0才会显示轨迹
    longitude: startLng,
    latitude: startLat,
    height: startHeight
  };
  
  // 设置当前设备类型和SN
  store.state.dock.deviceState.currentType = EDeviceTypeName.Aircraft;
  store.state.dock.deviceState.currentSn = testDroneSn;
  
  // 执行位置更新
  let pointIndex = 0;
  
  console.log('开始模拟无人机轨迹...');
  console.log(`将生成${pointsCount}个点，每${interval}毫秒更新一次，预计将触发${Math.floor(pointsCount/50)}次path2长度超过50的处理`);
  
  let intervalId = setInterval(() => {
    if (pointIndex >= testPoints.length) {
      clearInterval(intervalId);
      console.log('无人机轨迹测试完成');
      return;
    }
    
    const point = testPoints[pointIndex];
    
    // 更新store中的位置数据
    store.state.dock.deviceState.deviceInfo[testDroneSn].longitude = point.lng;
    store.state.dock.deviceState.deviceInfo[testDroneSn].latitude = point.lat;
    store.state.dock.deviceState.deviceInfo[testDroneSn].height = point.height;
    
    // 直接调用移动函数
    deviceTsaUpdateHook.moveTo(testDroneSn, point.lng, point.lat, point.height);
    
    // 每10个点记录一次日志，减少控制台输出
    if (pointIndex % 10 === 0) {
      console.log(`无人机位置更新: 点${pointIndex}/${pointsCount} [${point.lng}, ${point.lat}, ${point.height}]`);
    }
    
    pointIndex++;
  }, interval);
  
  // 返回清理函数
  return {
    stop: () => {
      clearInterval(intervalId);
      deviceTsaUpdateHook.removeMarker(testDroneSn);
      console.log('测试已停止并清理');
    },
    // 添加暂停和继续的功能
    pause: () => {
      clearInterval(intervalId);
      console.log('测试已暂停，当前进度：', pointIndex, '/', pointsCount);
    },
    resume: () => {
      if (pointIndex < pointsCount) {
        intervalId = setInterval(() => {
          if (pointIndex >= testPoints.length) {
            clearInterval(intervalId);
            console.log('无人机轨迹测试完成');
            return;
          }
          
          const point = testPoints[pointIndex];
          deviceTsaUpdateHook.moveTo(testDroneSn, point.lng, point.lat, point.height);
          
          if (pointIndex % 10 === 0) {
            console.log(`无人机位置更新: 点${pointIndex}/${pointsCount} [${point.lng}, ${point.lat}, ${point.height}]`);
          }
          
          pointIndex++;
        }, interval);
        console.log('测试已恢复');
      }
    }
  };
}