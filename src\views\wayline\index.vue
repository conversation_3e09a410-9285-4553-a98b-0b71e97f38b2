<template>
  <div class="left-panel-wrapper">
    <div class="project-panel">
      <div class="route-header">
        <span>航线 ({{ wayLineList.length }})</span>
        <div class="route-header-right">
          <a-tooltip title="导入航线">
            <FileAddOutlined @click="handleImportWayline" />
          </a-tooltip>
          <a-tooltip title="新建航线">
            <PlusOutlined @click="handleAddWayline" />
          </a-tooltip>
        </div>
      </div>
      <div class="project-list">
        <div v-for="item in wayLineList" :key="item.id" class="project-item">
          <div class="item-content">
            <p class="project-name">{{ item.name }}
              <EditOutlined @click="handleEdit(item)" />
            </p>
            <p class="item-desc">{{ DroneModelEnum[item.device_model_key] || '未知机型' }}</p>
            <p class="item-date">更新时间 {{ formatTime(item.update_time) }}</p>
          </div>
        </div>
      </div>
    </div>
    <waylineCreation v-if="showWaylineCreation" @close="closeWaylineCreation" @editWayline="initEditWayLine" />

    <!-- 导入航线 -->
    <a-modal :visible="isImport" title="导入航线数据" :closable="false" @cancel="onCancel" @ok="uploadFile"
      :style="{ top: '200px' }">
      <a-upload :multiple="false" :before-upload="beforeUpload" :show-upload-list="true" :file-list="fileList"
        :remove="removeFile" accept=".kmz">
        <a-button type="primary">
          <UploadOutlined />
          点击上传
        </a-button>
      </a-upload>
    </a-modal>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useGMapManage } from '@/hooks/use-c-map';
import { getApp } from "@/root";
import { getWayLineList, importFirmareFile } from '@/api/workspace';
import { EditOutlined, PlusOutlined, FileAddOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { dateFormat } from '@/utils/date';
import { DroneModelEnum } from '@/constants/drone';
import { useRoute } from 'vue-router';
import waylineCreation from '@/views/waylineCreaction/index.vue';

const route = useRoute();
const router = useRouter();
const wayLineList = ref([]);
const showWaylineCreation = ref(false);
const formatTime = (time) => {
  if (!time) {
    return ''
  }
  return dateFormat(new Date(time), 'yyyy-MM-dd hh:mm:ss')
}


const initWayLineList = async () => {
  const projectUuid = localStorage.getItem('projectUuid');
  const res = await getWayLineList({
    projectUuid,
  })
  if (res.data.code === 200) {
    wayLineList.value = res.data.data;
  }
}

onMounted(() => {
  initWayLineList();
})

const handleAddWayline = () => {
  showWaylineCreation.value = true;
}

const isImport = ref(false);
const fileList = ref([]);
const handleImportWayline = () => {
  console.log('导入航线');
  isImport.value = true;
}

function removeFile() {
  fileList.value = []
}

function onCancel() {
  fileList.value = []
  isImport.value = false
}

function beforeUpload(file) {
  if (!file.name || !file.name?.endsWith('.kmz')) {
    message.error('请上传 kmz 格式文件！')
    return false
  }
  fileList.value = [file]
  return false
}

const uploadFile = async () => {
  if (fileList.value.length === 0) {
    message.error('请上传航线文件！')
  }
  const file = fileList.value[0]
  const fileData = new FormData()
  fileData.append('file', file, file.name)
  const projectUuid = localStorage.getItem('projectUuid');
  const res = await importFirmareFile(projectUuid, fileData)
  if (res.data.code === 200) {
    message.success('导入航线成功!');
    isImport.value = false;
    initWayLineList();
  }
  fileList.value = []
}

const closeWaylineCreation = () => {
  showWaylineCreation.value = false;
  setTimeout(() => {
    initWayLineList();
  }, 100);
}

const initEditWayLine = async () => {
  await initWayLineList();
  handleEdit(wayLineList.value[0]);
}

const handleEdit = (item) => {
  router.push({
    path: '/waylineEditor/index',
    query: {
      id: item.id,
    },
  });
}
</script>

<style lang="scss" scoped>
.left-panel-wrapper {
  display: flex;
  flex-direction: column;
  width: 20%;
  flex-shrink: 0;
  border-right: 1px solid #3c3c3c;
}

.top-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 50px;
  border-bottom: 1px solid #3c3c3c;
  background-color: #232323;

  .title {
    font-size: 18px;
    font-weight: bold;
  }
}

.project-panel {
  flex-grow: 1;
  background-color: #232323;
  display: flex;
  flex-direction: column;
  padding: 0 15px;
  box-sizing: border-box;
  padding-top: 20px;
  overflow: hidden;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  font-size: 14px;
}

.route-header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.project-list {
  flex-grow: 1;
  overflow-y: auto;
  margin-right: -10px;
  padding-right: 10px;
}

.project-item {
  background-color: #2c2c2c;
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid transparent;
  transition: all 0.2s ease;

  &:hover {
    background-color: #383838;
    border-color: #555;
  }

  p {
    margin: 0;
  }

  .project-name {
    font-size: 14px;
    font-weight: 500;
    color: #f0f0f0;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .item-desc,
  .item-date {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 4px;
  }
}

/* Scrollbar styles for project-list */
.project-list::-webkit-scrollbar {
  width: 6px;
}

.project-list::-webkit-scrollbar-track {
  background: transparent;
}

.project-list::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.project-list::-webkit-scrollbar-thumb:hover {
  background: #666;
}
</style>