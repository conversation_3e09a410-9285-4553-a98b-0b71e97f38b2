<template>
    <div class="cockpit-wrapper">
        <!-- ******* 步骤 3 - START：提供组件所在的容器 ******* -->
        <div class="fh2-container">
            <div id="cockpit-header-container"></div>
            <div class="project-details router-cockpit">
                <div id="project-app-container"></div>
                <div id="project-middle-container"></div>
                <div id="project-right-micro-app" class="right-micro-app">
                    <div class="maps-micro-app">
                        <div class="cockpit-left-border-container"></div>
                        <div id="project-map-app-placeholder" class="map-app-placeholder">
                            <div class="cockpit-dock-live-container"></div>
                            <div id="map-app-global" class="map-app-container"></div>
                            <div class="cockpit-bottom-border-container"></div>
                        </div>
                    </div>
                    <div class="project-right">
                        <div id="cockpit-app-container"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- ******* 步骤 3 - END：提供组件所在的容器 ******* -->

        <!-- <div class="btn-bar">
            <button v-show="isCesiumReady" @click="addCustomCesiumData">地图定制</button>
            <button @click="changeTheme">样式定制</button>
        </div> -->
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { fh2Config, fh2PaasScriptUrl } from '@/config/fh2.js';
import { useRoute } from 'vue-router';

const route = useRoute();
const isCesiumReady = ref(false);
let paasScript = null;
let cesiumViewerSubscription = null;

// 动态加载 paas.js 脚本
const loadPaasScript = () => {
    return new Promise((resolve, reject) => {
        paasScript = document.createElement('script');
        paasScript.src = fh2PaasScriptUrl;
        paasScript.setAttribute('fh2', '');
        document.head.appendChild(paasScript);
        paasScript.onload = () => {
            resolve();
        };
        paasScript.onerror = () => {
            reject(new Error('Failed to load paas.js'));
        };
    });
};

// 步骤 6（按需）：自定义地图元素
const addCustomCesiumData = () => {
    if (!window.FH2 || !window.FH2.cesiumViewer) return;

    for (const key in window.FH2.cesiumViewer) {
        if (Object.prototype.hasOwnProperty.call(window.FH2.cesiumViewer, key)) {
            const viewer = window.FH2.cesiumViewer[key];
            if (viewer && viewer.entities) {
                viewer.entities.add({
                    position: window.Cesium.Cartesian3.fromDegrees(113.93, 22.57, 50),
                    label: {
                        text: "用户自定义文本",
                    },
                });
            }
        }
    }
};

// 步骤 7（按需）：自定义主题
const changeTheme = () => {
    document.body.className = document.body.className ? '' : 'set-change-color';
};

onMounted(async () => {
    try {
        await loadPaasScript();

        // 步骤 4: 使用组件前初始化配置
        window.FH2.initConfig({
            ...fh2Config,
        });

        // 步骤 5: 加载组件
        window.FH2.loadCockpit("cockpit-app-container", {
            gateway_sn: route.query.gatewaySn, // 按实际传入
            drone_sn: route.query.droneSn // 按实际传入
        });

        // 订阅地图实例变化事件
        cesiumViewerSubscription = window.FH2.subscribe('cesium-viewer-change', () => {
            if (window.FH2.cesiumViewer.global) {
                isCesiumReady.value = true;
            }
        });

    } catch (error) {
        console.error(error);
    }
});

onUnmounted(() => {
    // 组件卸载时移除脚本和取消订阅
    if (paasScript && paasScript.parentNode) {
        paasScript.parentNode.removeChild(paasScript);
    }
    if (cesiumViewerSubscription) {
        cesiumViewerSubscription(); // 假设返回的订阅对象是一个取消订阅的函数
    }
    // TODO: 如果FH2 SDK提供了卸载或清理方法，应该在这里调用
    // if (window.FH2 && typeof window.FH2.destroy === 'function') {
    //     window.FH2.destroy();
    // }
});
</script>

<style lang="scss" scoped>
/* 步骤 1: 添加组件容器样式 */
.cockpit-wrapper {
    height: 100%;
    width: 100%;
    margin: 0;
}

.fh2-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

#project-app-container>div,
:deep(#project-app-container>div),
.map-app-container>div,
:deep(.map-app-container>div) {
    width: 100%;
    height: 100%;
}

.project-details {
    position: relative;
    width: 100%;
    display: flex;
    flex: 1;
    overflow: auto;
}

.project-details .right-micro-app {
    position: relative;
    display: flex;
    flex: 1;
}

.project-details .right-micro-app .maps-micro-app {
    display: flex;
    flex: 1;
}

.project-details .right-micro-app .map-app-placeholder {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
}

.project-details .right-micro-app .map-app-container {
    height: 100%;
}

.btn-bar {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 9999;
    gap: 10px;

    button {
        padding: 8px 16px;
        border: 1px solid #ccc;
        background-color: #fff;
        cursor: pointer;
    }
}
</style>