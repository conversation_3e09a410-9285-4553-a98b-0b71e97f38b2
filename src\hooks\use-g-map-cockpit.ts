import store from '@/store'
import { getRoot } from '@/root'
import { ELocalStorageKey, EDeviceTypeName } from '@/types'
import { getDeviceBySn } from '@/api/manage'
import { message } from 'ant-design-vue'
import dockIcon from '@/assets/dock.png'
import rcIcon from '@/assets/rc.png'
import droneIcon from '@/assets/m30.png'

export function cockpitTsaUpdate() {
  const root = getRoot()
  let AMap = root.$aMap
  let defaultLng = 120.241193;
  let defaultLat = 30.299593;
  const icons = new Map([
    [EDeviceTypeName.Aircraft, droneIcon],
    [EDeviceTypeName.Gateway, rcIcon],
    [EDeviceTypeName.Dock, dockIcon]
  ])
  // const markers = store.state.dock.markerInfo.coverMap
  // const paths = store.state.dock.markerInfo.pathMap

  let markers = {}
  const paths = {}
  const paths2 = {};
  const polyLines = {};
  const oldLines = {};
  let trackLine = null as any

  function getTrackLineInstance() {
    if (!trackLine) {
      trackLine = new AMap.Polyline({
        map: root.$cockpitMap,
        strokeColor: '#0056ff' // 线颜色
      })
    }
    return trackLine
  }

  function newTrackLineInstance() {
    return new AMap.Polyline({
      map: root.$cockpitMap,
      strokeColor: 'rgba(41,213,81,0.9)' // 线颜色
    })
  }

  function initIcon(type: number) {
    return new AMap.Icon({
      image: icons.get(type),
      imageSize: new AMap.Size(40, 40),
      size: new AMap.Size(40, 40)
    })
  }

  function initMarker(type: number, name: string, sn: string, lng?: number, lat?: number) {
    if (markers[sn]) {
      return
    }
    if (!sn || !lng) {

      return
    }
    if (root.$cockpitMap === undefined) {
      return [defaultLng, defaultLat]
    }

    AMap = root.$aMap
    markers[sn] = new AMap.Marker({
      position: new AMap.LngLat(lng || defaultLng, lat || defaultLat),
      icon: initIcon(type),
      title: name,
      anchor: 'top-center',
      offset: [0, -20],
    })
    root.$cockpitMap.add(markers[sn])
    /*
     *  实时轨迹： 实现思路 每上报一次osd 根据前一个经纬度和当前的经纬度画一条新的线
     *  优化1： 每50个0sd画一次线，减少地图频繁添加图层卡顿
     *  优化2： 当图层达到一定量（5000） 将前面的线整合，合并成一条线.
     *  优化3：根据状态判断是否记录当前经纬度，比如不在飞行中或待机状态不记录
     */

    markers[sn].on('moving', function (e: any) {
      let modeCode = 0;
      if ((store.state.dock.deviceState.deviceInfo[sn] && store.state.dock.deviceState.deviceInfo[sn].mode_code)) {
        modeCode = store.state.dock.deviceState.deviceInfo[sn].mode_code;
      }


      if (modeCode && modeCode != 0) {
        let path = paths[sn]
        let path2 = paths2[sn];
        if (!path || !path2) {
          paths[sn] = e.passedPath;
          paths2[sn] = e.passedPath;
          return
        }
        if (polyLines[sn] && polyLines[sn].length > 5000) {
          for (let i = 0; i < polyLines[sn].length; i++) {
            root.$cockpitMap.remove(polyLines[sn][i])
          }
          if (oldLines[sn]) {
            for (let i = 0; i < oldLines[sn].length; i++) {
              root.$cockpitMap.remove(oldLines[sn][i])
            }
          }
          polyLines[sn] = [];
          oldLines[sn] = [];
          let timepolLine = oldLines[sn];
          let newLine = newTrackLineInstance();
          newLine.setPath(path);
          timepolLine.push(newLine);
          paths[sn] = []
        }
        path.push(e.passedPath[0]);
        path.push(e.passedPath[1]);
        path2.push(e.passedPath[0]);
        path2.push(e.passedPath[1]);

        if (path2.length > 50) {
          let line = newTrackLineInstance();
          line.setPath(path2);
          if (!polyLines[sn]) {
            polyLines[sn] = []
          }
          let polyLine = polyLines[sn];
          polyLine.push(line)
          paths2[sn] = e.passedPath
        }



      }

    })
  }

  function removeMarker(sn: string) {
    if (!markers[sn]) {
      return
    }
    root.$cockpitMap.remove(markers[sn])
    if (polyLines[sn]) {
      for (let i = 0; i < polyLines[sn].length; i++) {
        root.$cockpitMap.remove(polyLines[sn][i])
      }
    }

    if (oldLines[sn]) {
      for (let i = 0; i < oldLines[sn].length; i++) {
        root.$cockpitMap.remove(oldLines[sn][i])
      }
    }
    delete polyLines[sn]
    delete oldLines[sn]
    delete markers[sn]
    delete paths[sn]

  }

  function removeAllMarker() {
    // store.state.dock.markerInfo.coverMap = []
    for (let sn in markers) {
      removeMarker(sn)
    }
  }

  async function addMarker(sn: string, lng?: number, lat?: number) {
    if ( markers[sn]){
      return ;
    }
    getDeviceBySn(localStorage.getItem(ELocalStorageKey.WorkspaceId)!, sn)
      .then(data => {
        if (data.code !== 0) {
          message.error(data.message)
          return
        }
        initMarker(data.data.domain, data.data.nickname, sn, lng, lat)
      })
  }

  function moveTo(sn: string, lng: number, lat: number, modeCode?: number) {
    let marker = markers[sn]
    if (!marker) {
      addMarker(sn, lng || defaultLng, lat || defaultLat)
      return
    }
    // console.log("移动：{}",sn)
    // if (modeCode !==0){
    //
    marker.moveTo([lng || defaultLng, lat || defaultLat], {
      duration: 1800,
      autoRotation: true
    })
    // }

  }

  return {
    marker: markers,
    initMarker,
    removeMarker,
    removeAllMarker,
    moveTo,
    addMarker
  }
}