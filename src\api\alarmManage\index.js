import request from '@/axios';
import { getWorkspaceId } from '@/utils/storage'

export const getList = (current, size, params) => {
  return request({
    url: `/hztech-gong-di/aiAlarmRecord/list`,
    method: 'get',
    params: {
      ...params,
      current,
      size,
      workSpaceId: getWorkspaceId()
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: `/hztech-gong-di/aiAlarmRecord/detail`,
    method: 'get',
    params: {
      id,
      workSpaceId: getWorkspaceId()
    }
  })
}

export const remove = (ids) => {
  return request({
    url: `/hztech-gong-di/aiAlarmRecord/remove`,
    method: 'post',
    params: {
      ids,
      workspaceId: getWorkspaceId()
    }
  })
}

export const add = (row) => {
  return request({
    url: `/hztech-gong-di/aiAlarmRecord/save`,
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: `/hztech-gong-di/aiAlarmRecord/update`,
    method: 'post',
    data: row
  })
}

