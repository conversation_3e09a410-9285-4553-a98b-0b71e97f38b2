# CreatePlan.vue 修复说明

## 问题描述
当重复任务设置为"每一天"时，传参不正确。根据接口文档，应该传递 `repeat_type: 4`（每几天），但是原代码在处理每天重复时没有正确设置参数。

## 修复内容

### 1. 初始化修复
- 将 `repeat_type` 的初始值从 `0` 改为 `4`，与默认的 `repeat_frequency_unit: 'daily'` 保持一致

### 2. 添加任务类型监听器
- 添加了对 `planBody.task_type` 的监听，当任务类型变化时自动设置正确的 `repeat_type`
- 立即任务和单次定时任务：`repeat_type = 0`（不重复）
- 重复任务和连续任务：根据 `repeat_frequency_unit` 设置对应的 `repeat_type`

### 3. 参数提交逻辑优化
- 每天重复任务：不传递 `dow`、`dom`、`wom` 参数
- 每周重复任务：只传递 `dow` 参数
- 每月重复任务：根据按日期或按星期传递对应参数
- 非重复任务：清理所有重复相关参数

### 4. 表单重置优化
- 在 `setNull` 函数中重置所有重复相关参数

## 修复后的参数示例

### 每天重复任务
```json
{
  "repeat_type": 4,
  "interval": 1,
  "begin_at": 1752841560,
  "end_at": 1752854399,
  "extended_begin_at": [1752841560]
  // 不包含 dow, dom, wom 参数
}
```

### 每周重复任务
```json
{
  "repeat_type": 5,
  "interval": 1,
  "dow": [1, 2, 3],
  "begin_at": 1752841560,
  "end_at": 1752854399,
  "extended_begin_at": [1752841560]
  // 不包含 dom, wom 参数
}
```

### 每月重复任务（按日期）
```json
{
  "repeat_type": 6,
  "interval": 1,
  "dom": [1, 15, 30],
  "begin_at": 1752841560,
  "end_at": 1752854399,
  "extended_begin_at": [1752841560]
  // 不包含 dow, wom 参数
}
```

### 每月重复任务（按星期）
```json
{
  "repeat_type": 7,
  "interval": 1,
  "dow": [1],
  "wom": 2,
  "begin_at": 1752841560,
  "end_at": 1752854399,
  "extended_begin_at": [1752841560]
  // 不包含 dom 参数
}
```

## 测试建议
1. 测试每天重复任务的参数是否正确
2. 测试任务类型切换时参数重置是否正确
3. 测试表单重置功能是否正常
4. 测试不同重复类型的参数传递是否符合接口要求
