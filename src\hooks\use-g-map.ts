import AMapLoader from '@amap/amap-jsapi-loader'
import { App, reactive } from 'vue'
import { AMapConfig } from '@/constants/index'

export function useGMapManage () {
  const state = reactive({
    aMap: null, // Map类
    map: null, // 地图对象
    mouseTool: null,
    cockpitMap: null,
  })

  async function initMap (container: string, app:App,center?:any,type?:string) {
    AMapLoader.load({
      ...AMapConfig
    }).then((AMap) => {
      state.aMap = AMap
      state.map = new AMap.Map(container, {
        center: center ?? [120.241193, 30.299593],
        zoom: 20
      })
      state.mouseTool = new AMap.MouseTool(state.map)
      // 添加图层,type为TileLayer的子类方法 
      if (type) {
        state.map.setLayers([new AMap.TileLayer[type]()])
      }
      // 挂在到全局
      app.config.globalProperties.$aMap = state.aMap
      // 驾驶舱的地图
      if (container === 'cockpitMap') {
        app.config.globalProperties.$cockpitMap = state.map
      } else {
        app.config.globalProperties.$map = state.map
      }
      app.config.globalProperties.$mouseTool = state.mouseTool
    }).catch(e => {
      console.log(e)
    })
  }

  function globalPropertiesConfig (app:App,center?:any,name?:string,type?:string) {
    initMap(name || 'g-container', app,center,type)
	// initMap('mp-container', app)
  }

  return {
    globalPropertiesConfig,
  }
}
