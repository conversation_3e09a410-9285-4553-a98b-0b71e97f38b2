import { message, notification } from 'ant-design-vue'
import { MapDoodleEnum } from '@/types/map-enum'
import { getRoot } from '@/root'
import { PostFlightAreaBody, saveFlightArea } from '@/api/flight-area'
import { generateCircleContent, generatePolyContent } from '@/utils/map-layer-utils'
import { GeojsonCoordinate } from '@/utils/genjson'
import { uuidv4 } from '@/utils/uuid'
import { FlightAreasDroneLocation } from '@/types/flight-area'
import store from '@/store'
import { h } from 'vue'
import { useGMapCover } from '@/hooks/use-c-map-cover'
import moment from 'moment'
import { DATE_FORMAT } from '@/utils/constants'
import EventBus from '@/event-bus';
import { pinAMapPosition } from '@/types/map'

// 添加Cesium全局类型声明
declare const Cesium: any;

// 定义CommonHostWs类型
interface CommonHostWs<T> {
  sn: string;
  host: T;
  [key: string]: any;
}

// 定义扩展数据接口
interface ExtData {
  id?: string;
  name?: string;
  type?: string;
  mapType?: string;
  [key: string]: any;
}

// 默认飞行区域类型
const DEFAULT_FLIGHT_AREA_TYPE = 'dfence';

export function useFlightArea () {
  const root = getRoot()
  // const store = rootStore
  const coverMap = store.state.dock.coverMap

  let useGMapCoverHook = useGMapCover()

  const MIN_RADIUS = 10
  function checkCircle (obj: any): boolean {
    // 在Cesium中获取圆的半径
    const radius = obj.ellipse?.semiMajorAxis;
    if (radius < MIN_RADIUS) {
      message.error(`半径必须大于 ${MIN_RADIUS}m`)
      root.$viewer.entities.remove(obj)
      return false
    }
    return true
  }

  function checkPolygon (obj: any): boolean {
    // 在Cesium中获取多边形的点位
    const hierarchy = obj.polygon?.hierarchy?.getValue();
    const positions = hierarchy?.positions || [];
    if (positions.length < 3) {
      message.error('多边形的路径不能交叉')
      root.$viewer.entities.remove(obj)
      return false
    }
    return true
  }

  function setExtData (obj: any) {
    // 直接创建新的properties对象，而不是尝试修改现有的
    let ext: ExtData = {};
    
    // 如果已经有properties，获取其值
    if (obj.properties) {
      try {
        const props = obj.properties._value || obj.properties;
        // 处理可能的Cesium响应式对象
        const processedProps = {};
        Object.keys(props).forEach(key => {
          if (typeof props[key] === 'object' && props[key]?._value !== undefined) {
            processedProps[key] = props[key]._value;
          } else {
            processedProps[key] = props[key];
          }
        });
        ext = Object.assign({}, processedProps);
      } catch (e) {
        // 如果无法获取现有properties，使用空对象
        ext = {};
      }
    }
    
    // 确保type有值,但不覆盖已存在的type
    if (!ext.type) {
      ext.type = DEFAULT_FLIGHT_AREA_TYPE;
    }
    
    const id = uuidv4();
    const name = `${ext.type || 'area'}-${moment().format(DATE_FORMAT)}`;
    ext = Object.assign({}, ext, { id, name });
    
    // 直接设置properties为含所有所需属性的对象
    obj.properties = {
      ...ext,
      getValue: function() {
        return { ...ext };
      }
    };
    
    return ext;
  }

  function createFlightArea (obj: any) {
    // 获取Cesium实体的properties
    const ext = obj.properties.getValue ? obj.properties.getValue() : obj.properties;
    
    // 确保type字段存在且有效 - 处理Cesium响应式对象
    let areaType = ext.type;
    if (typeof areaType === 'object' && areaType._value !== undefined) {
      areaType = areaType._value;
    }
    
    if (!areaType) {
      areaType = DEFAULT_FLIGHT_AREA_TYPE;
      // 如果可以设置回对象，就更新对象的type
      if (obj.properties.getValue) {
        const currentProps = obj.properties.getValue();
        currentProps.type = DEFAULT_FLIGHT_AREA_TYPE;
      }
    }
    
    const data = {
      id: ext.id,
      type: areaType, // 使用处理后的type值
      name: ext.name,
    }
    let coordinates: GeojsonCoordinate | GeojsonCoordinate[][]
    let content

    // 检查mapType是否为undefined，如果是，则根据对象特性推断类型
    const mapType = ext.mapType || detectMapType(obj);
    
    if (!mapType) {
      message.error('无法识别的图形类型')
      root.$viewer.entities.remove(obj)
      return
    }

    switch (mapType) {
      case 'circle':
        // 获取Cesium圆的中心点和半径
        const center = obj.position.getValue();
        const cartographic = Cesium.Cartographic.fromCartesian(center);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const radius = obj.ellipse.semiMajorAxis.getValue();
        
        // 创建符合pinAMapPosition接口的对象
        const centerPosition: pinAMapPosition = {
          lng: longitude,
          lat: latitude,
          KL: 0,
          className: '',
          kT: 0
        };
        content = generateCircleContent(centerPosition, radius)
        coordinates = getWgs84(content.geometry.coordinates as GeojsonCoordinate)
        break
      case 'polygon':
        // 获取Cesium多边形的路径点
        const positions = obj.polygon.hierarchy.getValue().positions;
        // 转换为符合pinAMapPosition接口的坐标数组
        const pathArr = positions.map((position: any) => {
          const cartographic = Cesium.Cartographic.fromCartesian(position);
          const longitude = Cesium.Math.toDegrees(cartographic.longitude);
          const latitude = Cesium.Math.toDegrees(cartographic.latitude);
          return {
            lng: longitude,
            lat: latitude,
            KL: 0,
            className: '',
            kT: 0
          } as pinAMapPosition;
        });
        
        content = generatePolyContent(pathArr).content
        coordinates = [getWgs84(content.geometry.coordinates[0] as GeojsonCoordinate[])]
        break
      default:
        message.error(`无效的图形类型: ${mapType}`)
        root.$viewer.entities.remove(obj)
        return
    }
    content.geometry.coordinates = coordinates

    // 再次确认保存前的数据有效性
    if (!data.type) {
      data.type = DEFAULT_FLIGHT_AREA_TYPE;
    }

    // 打印发送到服务器的数据，帮助调试
    console.log('保存飞行区域数据:', Object.assign({}, data, { content }));

    saveFlightArea(Object.assign({}, data, { content }) as PostFlightAreaBody).then(res => {
      if (res.code !== 0) {
        useGMapCoverHook.removeCoverFromMap(ext.id)
      }else{
        const code = res.code
        EventBus.emit('flightList',code);
        EventBus.emit('updateMap')//自定义绘制实时回显
      }
    }).finally(() => root.$viewer.entities.remove(obj))
  }

  // 检测地图对象类型
  function detectMapType(obj: any): string | undefined {
    if (obj.ellipse) {
      return 'circle';
    } else if (obj.polygon) {
      return 'polygon';
    }
    return undefined;
  }

  function getDrawFlightAreaCallback (obj: any, flightAreaType?: string) {
    useGMapCoverHook = useGMapCover()
     
    // 根据对象特性设置mapType
    if (!obj.properties) {
      obj.properties = {};
    }
     
    // 检测对象类型并设置mapType
    let mapType;
    if (obj.ellipse) {
      mapType = MapDoodleEnum.CIRCLE;
    } else if (obj.polygon) {
      mapType = MapDoodleEnum.POLYGON;
    }
     
    // 设置初始properties
    if (mapType) {
      // 从对象中获取已有的type,如果没有则使用传入的flightAreaType或默认值
      let existingType;
      if (obj.properties?.type) {
        existingType = typeof obj.properties.type === 'object' && obj.properties.type._value !== undefined
          ? obj.properties.type._value
          : obj.properties.type;
      } else if (obj.properties?._value?.type) {
        existingType = typeof obj.properties._value.type === 'object' && obj.properties._value.type._value !== undefined
          ? obj.properties._value.type._value
          : obj.properties._value.type;
      }
      
      // 创建properties对象
      const properties = {
        mapType: mapType,
        type: existingType || flightAreaType || DEFAULT_FLIGHT_AREA_TYPE,
        getValue: function() {
          return {
            mapType: this.mapType,
            type: this.type
          };
        }
      };
      
      // 直接设置properties
      obj.properties = properties;
    }
     
    const ext = setExtData(obj)
    switch (mapType) {
      case MapDoodleEnum.CIRCLE:
        if (!checkCircle(obj)) {
          return
        }
        break
      case MapDoodleEnum.POLYGON:
        if (!checkPolygon(obj)) {
          return
        }
        break
      default:
        break
    }
    createFlightArea(obj)
  }

  const getWgs84 = <T extends GeojsonCoordinate | GeojsonCoordinate[]>(coordinate: T): T => {
    // 直接返回原始坐标，不进行转换
    return coordinate;
  }

  const getGcj02 = <T extends GeojsonCoordinate | GeojsonCoordinate[]>(coordinate: T): T => {
    // 直接返回原始坐标，不进行转换
    return coordinate;
  }

  const onFlightAreaDroneLocationWs = (data: CommonHostWs<FlightAreasDroneLocation>) => {
    const nearArea = data.host.drone_locations.filter(val => !val.is_in_area)
    const inArea = data.host.drone_locations.filter(val => val.is_in_area)
    notification.warning({
      key: `flight-area-${data.sn}`,
      message: `Drone(${data.sn}) flight area information`,
      placement: 'bottomLeft',
      description: h('div',
        [
          h('div', [
            h('span', { class: 'fz18' }, 'In the flight area: '),
            h('ul', [
              ...inArea.map(val => h('li', `There are ${val.area_distance} meters from the edge of the area(${coverMap[val.area_id]?.[1]?.getText() || val.area_id}).`))
            ])
          ]),
          h('div', [
            h('span', { class: 'fz18' }, 'Near the flight area: '),
            h('ul', [
              ...nearArea.map(val => h('li', `There are ${val.area_distance} meters from the edge of the area(${coverMap[val.area_id]?.[1]?.getText() || val.area_id}).`))
            ])
          ])
        ]),
      duration: null,
      style: {
        width: '420px',
        marginTop: '-8px',
        marginLeft: '-28px',
      }
    })
  }

  return {
    getDrawFlightAreaCallback,
    getGcj02,
    getWgs84,
    onFlightAreaDroneLocationWs,
  }
}
