<template>
    <div class="project-container">
        <left-sider />
        <div class="fh2-container-wrapper">
            <!-- ******* 接入步骤 3 - START：提供组件所在的容器 ******* -->
            <div class="fh2-container">
                <div class="project-details">
                    <div id="project-app-container"></div>
                    <div id="project-middle-container"></div>
                    <div id="project-right-micro-app" class="right-micro-app">
                        <div class="maps-micro-app">
                            <div id="project-map-app-placeholder" class="map-app-placeholder">
                                <div id="map-app-global" class="map-app-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ******* 接入步骤 3 - END：提供组件所在的容器 ******* -->

            <!-- <div class="btn-bar">
                <button id="CesiumMapBtn" @click="addCustomCesiumData" style="display: none;">地图定制</button>
                <button @click="changeTheme">样式定制</button>
            </div> -->
        </div>
        <a-modal v-model:open="isDeviceListVisible" title="设备列表" :footer="null" :width="500"
            wrapClassName="device-list-modal-wrapper">
            <div v-if="isDeviceListLoading" class="device-list-loading">
                <a-spin />
            </div>
            <div v-else class="device-list-modal-content">
                <div v-for="device in deviceList" :key="device.gateway.sn" class="device-item">
                    <div class="device-info">
                        <p><span>机场:</span> {{ device.gateway.callsign || '--' }}</p>
                        <p><span>设备名称:</span> {{ device.drone.callsign || '--' }}</p>
                    </div>
                    <button @click="openCockpit(device)" class="cockpit-btn"
                        :disabled="!device.gateway.sn || !device.drone.sn">进入驾驶舱</button>
                </div>
                <div v-if="!deviceList || !deviceList.length" class="no-data">
                    暂无设备
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { fh2Config } from '@/config/fh2.js';
import LeftSider from '@/components/left-sider/main.vue';
import { getDeviceListByProjectUuid, getDeviceState } from '@/api/workspace';
import { message } from 'ant-design-vue';

let observer = null;

// ******* 接入步骤 6（按需） - START：自定义地图元素（此为例子，具体使用场景请按实际） *******
function addCustomCesiumData() {
    if (!window.FH2 || !window.FH2.cesiumViewer || !window.Cesium) {
        console.error("FH2 or Cesium is not initialized.");
        return;
    }
    for (const key in window.FH2.cesiumViewer) {
        if (Object.prototype.hasOwnProperty.call(window.FH2.cesiumViewer, key)) {
            window.FH2.cesiumViewer[key].entities.add({
                position: window.Cesium.Cartesian3.fromDegrees(120.2367, 30.3020, 50),
                ellipse: {
                    semiMinorAxis: 100, // 圆的半径(米)
                    semiMajorAxis: 100, // 圆的半径(米)
                    material: window.Cesium.Color.LIGHTBLUE.withAlpha(0.7), // 淡蓝色半透明
                },
            });
        }
    }
}
// ******* 接入步骤 6（按需） - END：自定义地图元素 *******

// ******* 接入步骤 7（按需） - START：自定义主题 *******
function changeTheme() {
    document.body.classList.toggle('set-change-color');
}
// ******* 接入步骤 7（按需） - END：自定义主题 *******

onMounted(() => {
    // ******* 接入步骤 7（按需） - START：自定义主题 *******
    const styleLink = document.createElement('link');
    styleLink.id = 'custom-theme-style';
    styleLink.rel = 'stylesheet';
    styleLink.href = '/custom-style.css'; // vite serves from public dir at root
    document.head.appendChild(styleLink);
    // ******* 接入步骤 7（按需） - END：自定义主题 *******

    // ******* 接入步骤 2 - START：引入 paas.js（具体域名地址请按实际替换） *******
    const initFH2 = () => {
        if (!window.FH2) {
            console.error('FH2不可用');
            return;
        }
        
        // 初始化FH2配置
        window.FH2.initConfig({
            serverUrl: fh2Config.serverUrl,
            wssUrl: fh2Config.wssUrl,
            hostUrl: fh2Config.hostUrl,
            prjId: fh2Config.prjId,
            projectToken: fh2Config.projectToken,
        });
        
        window.FH2.loadProject("project-app-container");
        
        // 其他初始化代码
        initDeviceList();
        initClickHandler();
    };

    // 检查FH2是否已加载
    if (window.FH2) {
        initFH2();
    } else {
        // 如果还没加载，监听脚本加载完成事件
        window.addEventListener('FH2_LOADED', initFH2);
    }
});

const isDeviceListVisible = ref(false);
const isDeviceListLoading = ref(false);

const showDeviceList = () => {
    isDeviceListVisible.value = true;
};

const openCockpit = (device) => {
    if (device.gateway.sn && device.drone.sn) {
        window.open(`/cockpit/index?gatewaySn=${device.gateway.sn}&droneSn=${device.drone.sn}`, '_blank');
    } else {
        message.warn('设备信息不完整，无法进入驾驶舱');
    }
    isDeviceListVisible.value = false;
};

const initClickHandler = () => {
    const container = document.getElementById('project-app-container');
    const iconClickHandler = (event) => {
        // 查找当前点击元素的父级 device-item-content
        const deviceItemContent = event.currentTarget.closest('.device-item-content');
        if (deviceItemContent) {
            // 在找到的父元素中查找标题元素
            const titleElement = deviceItemContent.querySelector('.uranus-tsa-device-base-wrapper-title');
            if (titleElement) {
                // 输出标题内容
                console.log('设备标题:', titleElement.textContent.trim());
                const title = titleElement.textContent.trim();
                const deviceSn = title.split('-')[title.split('-').length - 1];
                console.log(deviceSn);
                const device = deviceList.value.find(item => item.gateway.sn === deviceSn);
                console.log(device);
                openCockpit(device);
            } else {
                console.log('未找到对应的标题元素');
            }
        } else {
            console.log('未找到设备容器元素');
        }
    };

    if (container) {
        observer = new MutationObserver(() => {
            const allIcons = document.querySelectorAll('.device-info-icon');
            allIcons.forEach(icon => {
                const hasListener = icon.hasAttribute('data-listener-added');
                const shouldHaveListener = icon.classList.contains('device-info-icon-bg') && !icon.classList.contains('device-info-icon-disabled');
                // const shouldHaveListener = true;
                if (shouldHaveListener && !hasListener) {
                    icon.addEventListener('click', iconClickHandler);
                    icon.setAttribute('data-listener-added', 'true');
                } else if (!shouldHaveListener && hasListener) {
                    icon.removeEventListener('click', iconClickHandler);
                    icon.removeAttribute('data-listener-added');
                }
            });
        });

        observer.observe(container, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class'],
        });
    }
}

const deviceList = ref([]);
const initDeviceList = () => {
    const projectUuid = localStorage.getItem('projectUuid');
    isDeviceListLoading.value = true;
    getDeviceListByProjectUuid({
        projectUuid,
    }).then(res => {
        deviceList.value = res.data.data;
        console.log(deviceList.value);
    }).catch(() => {
        message.error('获取设备列表失败');
    }).finally(() => {
        isDeviceListLoading.value = false;
    });
}

const initDeviceState = () => {
    const projectUuid = localStorage.getItem('projectUuid');
    getDeviceState({
        projectUuid,
        deviceSn: '1581F8HGX252C00A00UG',
    }).then(res => {
        console.log(res);
    });
}

onUnmounted(() => {
    if (observer) {
        observer.disconnect();
    }
    // Clean up on component unmount
    const script = document.getElementById('fh2-paas-script');
    if (script) {
        document.body.removeChild(script);
    }
    const styleLink = document.getElementById('custom-theme-style');
    if (styleLink) {
        document.head.removeChild(styleLink);
    }

    document.body.classList.remove('set-change-color');

    window.FH2.destroyProject();
});
</script>

<style lang="scss" scoped>
/* Using a wrapper to avoid interfering with body/html styles from other components */
.project-container {
    display: flex;
    height: 100%;
    width: 100%;
}

.fh2-container-wrapper {
    flex: 1;
    height: 100%;
    width: 100%;
}

.btn-bar {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 9999;
    gap: 10px;
}

/* ******* 接入步骤 1 - START：添加组件容器样式 ******* */
.fh2-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

:deep(#project-app-container>div),
:deep(.map-app-container>div) {
    width: 100%;
    height: 100%;
}

.project-details {
    position: relative;
    width: 100%;
    display: flex;
    flex: 1;
    overflow: auto;
}

.project-details .right-micro-app {
    position: relative;
    display: flex;
    flex: 1;
}

.project-details .right-micro-app .maps-micro-app {
    display: flex;
    flex: 1;
}

.project-details .right-micro-app .map-app-placeholder {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
}

.project-details .right-micro-app .map-app-container {
    height: 100%;
}

/* ******* 接入步骤 1 - END：添加组件容器样式 ******* */

.device-list-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
}

:deep(.device-list-modal-wrapper) {
    .ant-modal-content {
        background-color: #2c3036;
        color: #fff;
        border-radius: 8px;
    }

    .ant-modal-header {
        background-color: #2c3036;
        color: #fff;
        border-bottom: 1px solid #444;
        border-radius: 8px 8px 0 0;
    }

    .ant-modal-title {
        color: #fff;
    }

    .ant-modal-close {
        color: #fff;
    }

    .ant-modal-body {
        padding-top: 10px;
        padding-bottom: 20px;
    }
}

.device-list-modal-content {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
    margin-right: -10px;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }
}

.device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 4px;
    border-bottom: 1px solid #444;

    &:last-child {
        border-bottom: none;
    }
}

.device-info {
    p {
        margin: 0 0 4px 0;
        font-size: 14px;
        line-height: 1.5;

        &:last-child {
            margin-bottom: 0;
        }

        span {
            color: #ccc;
            margin-right: 8px;
        }
    }
}

.cockpit-btn {
    background-color: #409eff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    flex-shrink: 0;
    margin-left: 16px;

    &:hover {
        background-color: #66b1ff;
    }

    &:disabled {
        background-color: #5f6164;
        cursor: not-allowed;
        opacity: 0.7;
    }
}

.no-data {
    text-align: center;
    padding: 20px;
    color: #999;
}
</style>