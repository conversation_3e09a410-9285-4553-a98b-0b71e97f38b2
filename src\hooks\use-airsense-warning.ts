import { onMounted, onUnmounted } from 'vue'
import EventBus from '@/event-bus'
import { getRoot } from '@/root'
import i18n from '@/lang'
import { IAirsenseWarningPayload, IAirsenseWarning } from '@/types/airsense'

const root = getRoot()

function handleAirsenseWarning(payload: IAirsenseWarningPayload) {
  if (!payload.data || !payload.data.data) {
    return
  }

  payload.data.data.forEach((warning: IAirsenseWarning) => {
    if (warning.warning_level >= 1) {
      (root as any).$notify({
        title: root.$t('airsense.warningTitle'),
        message: root.$t('airsense.warningMessage', {
          icao: warning.icao,
          distance: warning.distance,
          relative_altitude: warning.relative_altitude,
        }),
        type: 'warning',
        duration: 10000,
      });
    }
  });
}

export function useAirsenseWarning() {
  onMounted(() => {
    EventBus.on('airsenseWarningWs', handleAirsenseWarning)
  })

  onUnmounted(() => {
    EventBus.off('airsenseWarningWs', handleAirsenseWarning)
  })
} 