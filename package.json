{"name": "hztech", "version": "2.4.0", "scripts": {"dev": "vite --host", "prod": "vite --mode production", "build": "vite build", "build:prod": "vite build --mode production", "serve": "vite preview --host"}, "dependencies": {"@cesium-china/cesium-map": "^1.0.0", "@element-plus/icons-vue": "^2.3.1", "@saber/nf-design-base-elp": "^1.3.0", "@saber/nf-form-design-elp": "^1.4.0", "@saber/nf-form-elp": "^1.4.0", "@smallwei/avue": "^3.5.6", "animate.css": "^4.1.1", "ant-design-vue": "4.x", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "cesium": "^1.128.0", "codemirror": "^5.65.16", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "disable-devtool": "^0.3.8", "element-plus": "^2.8.7", "eventemitter3": "^5.0.1", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "jspdf": "^3.0.1", "mitt": "^3.0.1", "moment": "^2.30.1", "mqtt": "^4.3.7", "nprogress": "^0.2.0", "qs": "^6.14.0", "reconnecting-websocket": "^4.4.0", "sm-crypto": "^0.3.13", "vue": "^3.4.27", "vue-i18n": "^9.1.9", "vue-router": "^4.3.2", "vue3-clipboard": "^1.0.0", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.27", "prettier": "^2.8.7", "sass": "^1.77.2", "terser": "^5.31.1", "unplugin-auto-import": "^0.11.2", "unplugin-vue-components": "^28.8.0", "vite": "^5.2.12", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}