import request from '@/axios';
import { getProjectUuid } from '@/utils/storage';

export const getUsualList = (params) => {
  return request({
    url: '/hztech-sk2/tenantProject/page',
    method: 'get',
    params,
  });
};

export const getWayLineList = (params) => {
  return request({
    url: '/hztech-sk-api/v0.1/wayLine/wayLineList',
    method: 'get',
    params: {
      projectUuid: getProjectUuid(),
      ...params
    },
  });
};

export const importFirmareFile = (projectUuid, data) => {
  return request({
    url: `/hztech-sk-api/v0.1/wayLine/wayLineFileUpload?projectUuid=${projectUuid}`,
    method: 'post',
    data,
  });
};

export const getDeviceListByProjectUuid = (params) => {
  return request({
    url: '/hztech-sk-api/v0.1/organization/deviceListByProjectUuid',
    method: 'get',
    params,
  });
};

export const getDeviceState = (params) => {
  return request({
    url: '/hztech-sk-api/v0.1/device/state',
    method: 'get',
    params,
  });
};

