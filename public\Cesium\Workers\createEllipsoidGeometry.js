define(["./defaultValue-0a909f67","./EllipsoidGeometry-2ba9e2d6","./Transforms-01e95659","./Matrix3-a348023f","./Math-e97915da","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./ComponentDatatype-77274976","./WebGLConstants-a8cc3e8c","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryOffsetAttribute-04332ce7","./IndexDatatype-2149f06c","./VertexFormat-ab2e00e6"],(function(e,t,r,a,o,i,n,c,f,m,u,d,s,y,l){"use strict";return function(r,a){return e.defined(a)&&(r=t.EllipsoidGeometry.unpack(r,a)),t.EllipsoidGeometry.createGeometry(r)}}));
