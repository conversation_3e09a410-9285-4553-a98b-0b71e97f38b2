import { GeojsonCoordinate } from '../utils/genjson'
import { getRoot } from '@/root'
import * as Cesium from 'cesium'

export function useMapTool () {
  const root = getRoot();
  /**
   * 将地图视图平移到指定坐标
   * @param coordinate 地理坐标 [经度, 纬度]
   */
  function panTo (coordinate: GeojsonCoordinate) {
    const map = root.$viewer;
    // 使用Cesium相机飞行到指定位置
    map.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1], 200),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0
      },
      duration: 1 // 飞行时间(秒)
    });
  }
  
  return {
    panTo,
  }
}
