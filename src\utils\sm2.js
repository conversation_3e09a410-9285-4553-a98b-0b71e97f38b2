import website from '@/config/website';
import { sm2 } from 'sm-crypto';

/**
 * sm2 加密方法
 * @param data
 * @returns {*}
 */
export function encrypt(data) {
  try {
    return sm2.doEncrypt(data, website.oauth2.publicKey, 0);
  } catch {
    return '';
  }
}
/**
 * sm2 解密方法
 * @param data
 * @returns {*}
 */
export function decrypt(data) {
  try {
    // 检查输入数据
    if (!data || typeof data !== 'string' || data.trim() === '') {
      console.warn('SM2解密：输入数据为空或无效');
      return '';
    }
    
    // 检查是否有有效的私钥
    if (!website.oauth2.privateKey || website.oauth2.privateKey.trim() === '') {
      console.error('SM2解密：未配置有效的私钥');
      return '';
    }
    
    const result = sm2.doDecrypt(data, website.oauth2.privateKey, 0);
    return result;
  } catch (error) {
    console.error('SM2解密错误:', error);
    return '';
  }
}
