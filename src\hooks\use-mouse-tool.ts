import { reactive, ref } from 'vue'
import pin2d8cf0 from '@/assets/pin-2d8cf0.svg'
import { MapDoodleType } from '@/constants/map'
import { getRoot } from '@/root'
import { MapDoodleEnum } from '@/types/map-enum'
import { EFlightAreaType } from '@/types/flight-area'
import { message } from 'ant-design-vue'
import store from '@/store'

export function useMouseTool() {
  const root = getRoot()
  const state = reactive({
    pinNum: 0,
    polylineNum: 0,
    PolygonNum: 0,
    currentType: '',
    isDrawing: false,
    currentPoints: [] as Cesium.Cartesian3[],
    currentEntity: null as Cesium.Entity | null,
    currentLineEntity: null as Cesium.Entity | null,
    callback: null as Function | null,
    flightAreaType: null as EFlightAreaType | null,
  })

  const flightAreaColorMap = {
    [EFlightAreaType.DFENCE]: '#19be6b',
    [EFlightAreaType.NFZ]: '#ff0000',
  }

  // 初始化鼠标事件处理器
  function initEventHandlers() {
    if (!root?.$viewer) return

    // 左键点击添加点
    root.$viewer.screenSpaceEventHandler.setInputAction((click: any) => {
      if (!state.isDrawing) return
      
      const cartesian = getCartesianPosition(click.position)
      if (!cartesian) return
      
      state.currentPoints.push(cartesian)
      updatePreviewEntity()
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

    // 右键点击完成绘制
    root.$viewer.screenSpaceEventHandler.setInputAction((click: any) => {
      if (!state.isDrawing) return
      
      // 在画圆形时，使用右键点击来确定半径并完成绘制
      if (state.currentType === MapDoodleEnum.CIRCLE && state.currentPoints.length === 1) {
        const cartesian = getCartesianPosition(click.position)
        if (cartesian) {
          state.currentPoints.push(cartesian)
          updatePreviewEntity()
        }
      }
      
      finishDrawing()
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)

    // 鼠标移动时更新预览
    root.$viewer.screenSpaceEventHandler.setInputAction((movement: any) => {
      if (!state.isDrawing || state.currentPoints.length === 0) return
      
      const cartesian = getCartesianPosition(movement.endPosition)
      if (!cartesian) return
      
      updateMovingPreview(cartesian)
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
  }

  // 获取鼠标点击位置的笛卡尔坐标
  function getCartesianPosition(position: Cesium.Cartesian2): Cesium.Cartesian3 | undefined {
    if (!root?.$viewer) return undefined
    
    // 从鼠标点击位置获取地球表面的坐标
    const ray = root.$viewer.camera.getPickRay(position)
    if (!ray) return undefined
    
    return root.$viewer.scene.globe.pick(ray, root.$viewer.scene)
  }

  // 更新当前绘制的预览实体
  function updatePreviewEntity() {
    if (!root?.$viewer || state.currentPoints.length === 0) return
    
    if (state.currentEntity) {
      root.$viewer.entities.remove(state.currentEntity)
      state.currentEntity = null
    }
    
    if (state.currentLineEntity) {
      root.$viewer.entities.remove(state.currentLineEntity)
      state.currentLineEntity = null
    }

    switch (state.currentType) {
      case MapDoodleEnum.PIN:
        // 标记点只需要一个点，所以立即完成
        if (state.currentPoints.length === 1) {
          finishDrawing()
        }
        break
      case MapDoodleEnum.POLYLINE:
        // 创建线预览
        state.currentEntity = root.$viewer.entities.add({
          polyline: {
            positions: state.currentPoints,
            width: 2,
            material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0'))
          }
        })
        break
      case MapDoodleEnum.POLYGON:
        // 创建多边形预览
        if (state.currentPoints.length >= 2) {
          state.currentEntity = root.$viewer.entities.add({
            polygon: {
              hierarchy: new Cesium.PolygonHierarchy(state.currentPoints),
              material: new Cesium.ColorMaterialProperty(
                Cesium.Color.fromCssColorString('#1791fc').withAlpha(0.4)
              ),
              outline: true,
              outlineColor: Cesium.Color.fromCssColorString('#2d8cf0'),
              outlineWidth: 2
            }
          })
          
          // 添加多边形边框线，防止闪烁问题
          state.currentLineEntity = root.$viewer.entities.add({
            polyline: {
              positions: [...state.currentPoints, state.currentPoints[0]],
              width: 2,
              material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0'))
            }
          })
        } else if (state.currentPoints.length === 1) {
          // 只有一个点时，也创建线段预览以提供视觉反馈
          state.currentLineEntity = root.$viewer.entities.add({
            polyline: {
              positions: [state.currentPoints[0], state.currentPoints[0]],
              width: 2,
              material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0'))
            }
          })
        }
        break
      case MapDoodleEnum.CIRCLE:
        // 圆形在Flight Area模式下使用
        if (state.currentPoints.length >= 2) {
          const center = state.currentPoints[0]
          const edge = state.currentPoints[1]
          const radius = Cesium.Cartesian3.distance(center, edge)
          
          state.currentEntity = root.$viewer.entities.add({
            position: center,
            ellipse: {
              semiMajorAxis: radius,
              semiMinorAxis: radius,
              material: new Cesium.ColorMaterialProperty(
                Cesium.Color.fromCssColorString(flightAreaColorMap[state.flightAreaType] || '#1791fc').withAlpha(
                  state.flightAreaType === EFlightAreaType.NFZ ? 0.3 : 0
                )
              ),
              outline: true,
              outlineColor: Cesium.Color.fromCssColorString(flightAreaColorMap[state.flightAreaType] || '#2d8cf0'),
              outlineWidth: 4,
              height: 0
            }
          })
        }
        break
    }
  }

  // 鼠标移动时更新移动预览
  function updateMovingPreview(position: Cesium.Cartesian3) {
    if (!root?.$viewer || !state.isDrawing) return

    const points = [...state.currentPoints, position]
    
    if (state.currentEntity) {
      root.$viewer.entities.remove(state.currentEntity)
      state.currentEntity = null
    }
    
    if (state.currentLineEntity) {
      root.$viewer.entities.remove(state.currentLineEntity)
      state.currentLineEntity = null
    }

    switch (state.currentType) {
      case MapDoodleEnum.POLYLINE:
        state.currentEntity = root.$viewer.entities.add({
          polyline: {
            positions: points,
            width: 2,
            material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0'))
          }
        })
        break
      case MapDoodleEnum.POLYGON:
        if (points.length >= 3) {
          // 绘制多边形面
          state.currentEntity = root.$viewer.entities.add({
            polygon: {
              hierarchy: new Cesium.PolygonHierarchy(points),
              material: new Cesium.ColorMaterialProperty(
                Cesium.Color.fromCssColorString('#1791fc').withAlpha(0.4)
              ),
              outline: true,
              outlineColor: Cesium.Color.fromCssColorString('#2d8cf0'),
              outlineWidth: 2
            }
          })
          
          // 额外绘制多边形边框线，避免闪烁问题
          const linePoints = [...points, points[0]]
          state.currentLineEntity = root.$viewer.entities.add({
            polyline: {
              positions: linePoints,
              width: 2,
              material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0'))
            }
          })
        } else if (points.length === 2) {
          // 只有两个点时，绘制线段
          state.currentLineEntity = root.$viewer.entities.add({
            polyline: {
              positions: points,
              width: 2,
              material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0'))
            }
          })
        }
        break
      case MapDoodleEnum.CIRCLE:
        if (points.length >= 2) {
          const center = points[0]
          const edge = position
          const radius = Cesium.Cartesian3.distance(center, edge)
          
          state.currentEntity = root.$viewer.entities.add({
            position: center,
            ellipse: {
              semiMajorAxis: radius,
              semiMinorAxis: radius,
              material: new Cesium.ColorMaterialProperty(
                Cesium.Color.fromCssColorString(flightAreaColorMap[state.flightAreaType] || '#1791fc').withAlpha(
                  state.flightAreaType === EFlightAreaType.NFZ ? 0.3 : 0
                )
              ),
              outline: true,
              outlineColor: Cesium.Color.fromCssColorString(flightAreaColorMap[state.flightAreaType] || '#2d8cf0'),
              outlineWidth: 4,
              height: 0
            }
          })
        }
        break
    }
  }

  // 完成绘制过程
  function finishDrawing() {
    if (!root?.$viewer || !state.isDrawing) return
    
    if (state.currentEntity) {
      root.$viewer.entities.remove(state.currentEntity)
    }
    
    if (state.currentLineEntity) {
      root.$viewer.entities.remove(state.currentLineEntity)
    }

    const result = createFinalEntity()
    
    // 回调传递结果
    if (state.callback && result) {
      state.callback({ obj: result })
    }
    
    // 重置状态
    resetDrawingState()
  }

  // 创建最终的实体
  function createFinalEntity() {
    if (!root?.$viewer || state.currentPoints.length === 0) return null
    
    let entity = null
    
    switch (state.currentType) {
      case MapDoodleEnum.PIN:
        entity = root.$viewer.entities.add({
          position: state.currentPoints[0],
          billboard: {
            image: pin2d8cf0,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          },
          title: `${MapDoodleEnum.PIN}${state.pinNum}`
        })
        state.pinNum++
        break
      case MapDoodleEnum.POLYLINE:
        if (state.currentPoints.length < 2) {
          message.warning('请至少绘制两个点')
          return null
        }
        entity = root.$viewer.entities.add({
          polyline: {
            positions: state.currentPoints,
            width: 2,
            material: new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString('#2d8cf0')),
            clampToGround: true
          },
          title: `${MapDoodleEnum.POLYLINE}${state.polylineNum++}`
        })
        break
      case MapDoodleEnum.POLYGON:
        if (state.currentPoints.length < 3) {
          message.warning('请至少绘制三个点')
          return null
        }
        entity = root.$viewer.entities.add({
          polygon: {
            hierarchy: new Cesium.PolygonHierarchy(state.currentPoints),
            material: new Cesium.ColorMaterialProperty(
              Cesium.Color.fromCssColorString('#1791fc').withAlpha(0.4)
            ),
            outline: true,
            outlineColor: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2
          },
          title: `${MapDoodleEnum.POLYGON}${state.PolygonNum++}`
        })
        break
      case MapDoodleEnum.CIRCLE:
        if (state.currentPoints.length < 2) return null
        
        const center = state.currentPoints[0]
        const edge = state.currentPoints[1]
        const radius = Cesium.Cartesian3.distance(center, edge)
        
        entity = root.$viewer.entities.add({
          position: center,
          ellipse: {
            semiMajorAxis: radius,
            semiMinorAxis: radius,
            material: new Cesium.ColorMaterialProperty(
              Cesium.Color.fromCssColorString(flightAreaColorMap[state.flightAreaType] || '#1791fc').withAlpha(
                state.flightAreaType === EFlightAreaType.NFZ ? 0.3 : 0
              )
            ),
            outline: true,
            outlineColor: Cesium.Color.fromCssColorString(flightAreaColorMap[state.flightAreaType] || '#2d8cf0'),
            outlineWidth: 4,
            height: 0
          },
          properties: {
            type: state.flightAreaType,
            mapType: 'circle',
            radius: radius
          }
        })
        break
    }
    
    return entity
  }

  // 重置绘制状态
  function resetDrawingState() {
    state.isDrawing = false
    state.currentPoints = []
    state.currentEntity = null
    state.currentLineEntity = null
    state.callback = null
    state.flightAreaType = null
  }

  // 绘制点标记
  function drawPin(type: MapDoodleType, getDrawCallback: Function) {
    state.isDrawing = true
    state.currentType = type
    state.callback = getDrawCallback
    
    if (!root?.$viewer) {
      message.error('Viewer not initialized')
      resetDrawingState()
      return
    }
    
    // 初始化事件处理
    initEventHandlers()
    
    message.info('左键点击添加标记点，右键完成')
  }

  // 绘制线
  function drawPolyline(type: MapDoodleType, getDrawCallback: Function) {
    state.isDrawing = true
    state.currentType = type
    state.callback = getDrawCallback
    
    if (!root?.$viewer) {
      message.error('Viewer not initialized')
      resetDrawingState()
      return
    }
    
    // 初始化事件处理
    initEventHandlers()
    
    message.info('左键点击添加线段点，右键完成绘制')
  }

  // 绘制多边形
  function drawPolygon(type: MapDoodleType, getDrawCallback: Function) {
    state.isDrawing = true
    state.currentType = type
    state.callback = getDrawCallback
    
    if (!root?.$viewer) {
      message.error('Viewer not initialized')
      resetDrawingState()
      return
    }
    
    // 初始化事件处理
    initEventHandlers()
    
    message.info('左键点击添加多边形顶点，右键完成绘制')
  }

  // 停止绘制
  function drawOff(type: MapDoodleType) {
    resetDrawingState()
  }

  // 绘制飞行区域多边形
  function drawFlightAreaPolygon(type: EFlightAreaType, getDrawFlightAreaCallback: Function) {
    state.isDrawing = true
    state.currentType = MapDoodleEnum.POLYGON
    state.callback = getDrawFlightAreaCallback
    state.flightAreaType = type
    
    if (!root?.$viewer) {
      message.error('Viewer not initialized')
      resetDrawingState()
      return
    }
    
    // 初始化事件处理
    initEventHandlers()
    
    message.info('左键点击添加多边形顶点，右键完成绘制')
  }

  // 绘制飞行区域圆形
  function drawFlightAreaCircle(type: EFlightAreaType, getDrawFlightAreaCallback: Function) {
    state.isDrawing = true
    state.currentType = MapDoodleEnum.CIRCLE
    state.callback = getDrawFlightAreaCallback
    state.flightAreaType = type
    
    if (!root?.$viewer) {
      message.error('Viewer not initialized')
      resetDrawingState()
      return
    }
    
    // 初始化事件处理
    initEventHandlers()
    
    message.info('左键点击添加圆心，右键点击确定半径并完成绘制')
  }

  // 主要入口函数，根据类型调用不同的绘制函数
  function mouseTool(type: MapDoodleType, getDrawCallback: Function, flightAreaType?: EFlightAreaType) {
    state.currentType = type
    
    // 自定义飞行区域绘制
    if (flightAreaType) {
      switch (type) {
        case MapDoodleEnum.POLYGON:
          drawFlightAreaPolygon(flightAreaType, getDrawCallback)
          return
        case MapDoodleEnum.CIRCLE:
          drawFlightAreaCircle(flightAreaType, getDrawCallback)
          return
        default:
          message.error(`无效的类型: ${flightAreaType}`)
          return
      }
    }
    
    // 非自定义飞行区域绘制
    switch (type) {
      case MapDoodleEnum.PIN:
        drawPin(type, getDrawCallback)
        break
      case MapDoodleEnum.POLYLINE:
        drawPolyline(type, getDrawCallback)
        break
      case MapDoodleEnum.POLYGON:
        drawPolygon(type, getDrawCallback)
        break
      case MapDoodleEnum.Close:
        drawOff(type)
        break
    }
  }

  return {
    mouseTool
  }
}
