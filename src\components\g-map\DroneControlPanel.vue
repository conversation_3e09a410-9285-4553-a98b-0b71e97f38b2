<template>
  <div class="drone-control-wrapper">
    <div class="drone-control-header">
      <span>无人机控制</span>
      <span class="active_circle" :style="isHeartbeatActive ? 'background: rgb(25,190,107);' : ' background: red;'"></span>
    </div>
    <div class="drone-control-box">
      <div class="box">
        <div class="row">
          <div class="drone-control">
            <Button ghost size="small" @click="enterFlightControl">{{ '进入远程控制' }}</Button>
            <Button ghost size="small" @click="exitFlightCOntrol" style="color: #ffffff;">{{ '退出远程控制' }}</Button>
          </div>
        </div>
        <div class="row">
          <div class="drone-control-direction">
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.KEY_Q)" @onmouseup="onMouseUp(KeyCode.KEY_Q)">
              <template #icon>
                <UndoOutlined/>
              </template>
              <span class="word">Q</span>
            </Button>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.KEY_W)" @onmouseup="onMouseUp(KeyCode.KEY_W)">
              <template #icon>
                <UpOutlined/>
              </template>
              <span class="word">W</span>
            </Button>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.KEY_E)" @onmouseup="onMouseUp(KeyCode.KEY_E)">
              <template #icon>
                <RedoOutlined/>
              </template>
              <span class="word">E</span>
            </Button>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.ARROW_UP)" @onmouseup="onMouseUp(KeyCode.ARROW_UP)">
              <template #icon>
                <ArrowUpOutlined/>
              </template>
            </Button>
            <br/>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.KEY_A)" @onmouseup="onMouseUp(KeyCode.KEY_A)">
              <template #icon>
                <LeftOutlined/>
              </template>
              <span class="word">A</span>
            </Button>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.KEY_S)" @onmouseup="onMouseUp(KeyCode.KEY_S)">
              <template #icon>
                <DownOutlined/>
              </template>
              <span class="word">S</span>
            </Button>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.KEY_D)" @onmouseup="onMouseUp(KeyCode.KEY_D)">
              <template #icon>
                <RightOutlined/>
              </template>
              <span class="word">D</span>
            </Button>
            <Button size="small" ghost @mousedown="onMouseDown(KeyCode.ARROW_DOWN)" @onmouseup="onMouseUp(KeyCode.ARROW_DOWN)">
              <template #icon>
                <ArrowDownOutlined/>
              </template>
            </Button>
          </div>
          <Button type="primary" size="small" danger ghost @click="handleEmergencyStop">
            <template #icon>
              <PauseCircleOutlined/>
            </template>
            <span>Break</span>
          </Button>
        </div>
        <div class="row">
          <DroneControlPopover
              :visible="flyToPointPopoverData.visible"
              :loading="flyToPointPopoverData.loading"
              @confirm="($event) => onFlyToConfirm(true)"
              @cancel="($event) => onFlyToConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">目标点纬度:</span>
                  <a-input-number v-model:value="flyToPointPopoverData.latitude"/>
                </div>
                <div>
                  <span class="form-label">目标点经度:</span>
                  <a-input-number v-model:value="flyToPointPopoverData.longitude"/>
                </div>
                <div>
                  <!--目标点高度（椭球高），使用 WGS84 模型-->
                  <span class="form-label">目标点高度(m):</span>
                  <a-input-number v-model:value="flyToPointPopoverData.height"/>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="onShowFlyToPopover">
              <span>飞向目标点</span>
            </Button>
          </DroneControlPopover>
          <Button size="small" ghost @click="onStopFlyToPoint">
            <span>停止飞向目标点</span>
          </Button>
          <DroneControlPopover
              :visible="takeoffToPointPopoverData.visible"
              :loading="takeoffToPointPopoverData.loading"
              @confirm="($event) => onTakeoffToPointConfirm(true)"
              @cancel="($event) => onTakeoffToPointConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">目标点纬度:</span>
                  <a-input-number v-model:value="takeoffToPointPopoverData.latitude"/>
                </div>
                <div>
                  <span class="form-label">目标点经度:</span>
                  <a-input-number v-model:value="takeoffToPointPopoverData.longitude"/>
                </div>
                <div>
                  <!--目标点高度（椭球高），使用 WGS84 模型，飞行器到点后默认行为：悬停-->
                  <span class="form-label">目标点高度(m):</span>
                  <a-input-number v-model:value="takeoffToPointPopoverData.height"/>
                </div>
                <div>
                  <!--相对(机场)起飞点的高度（ALT），飞行器先升到特定的高度，然后再飞向目标点。-->
                  <span class="form-label">安全起飞高度(m):</span>
                  <a-input-number v-model:value="takeoffToPointPopoverData.securityTakeoffHeight"/>
                </div>
                <div>
                  <!--相对(机场)起飞点的高度，相对高 ALT-->
                  <span class="form-label">返回机场高度(m):</span>
                  <a-input-number v-model:value="takeoffToPointPopoverData.rthAltitude"/>
                </div>
                <div>
                  <span class="form-label">遥控器失控动作:</span>
                  <a-select
                      v-model:value="takeoffToPointPopoverData.rcLostAction"
                      style="width: 120px"
                      :options="LostControlActionInCommandFLightOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">航线失联动作:</span>
                  <a-select
                      v-model:value="takeoffToPointPopoverData.exitWaylineWhenRcLost"
                      style="width: 120px"
                      :options="WaylineLostControlActionInCommandFlightOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">返回机场模式:</span>
                  <a-select
                      v-model:value="takeoffToPointPopoverData.rthMode"
                      style="width: 120px"
                      :options="RthModeInCommandFlightOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">指点飞行失控动作:</span>
                  <a-select
                      v-model:value="takeoffToPointPopoverData.commanderModeLostAction"
                      style="width: 120px"
                      :options="CommanderModeLostActionInCommandFlightOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">指点飞行模式:</span>
                  <a-select
                      v-model:value="takeoffToPointPopoverData.commanderFlightMode"
                      style="width: 120px"
                      :options="CommanderFlightModeInCommandFlightOptions"
                  ></a-select>
                </div>
                <div>
                  <!--相对(机场)起飞点的高度，相对高 ALT-->
                  <span class="form-label">指点飞行高度(m):</span>
                  <a-input-number v-model:value="takeoffToPointPopoverData.commanderFlightHeight"/>
                </div>
              </div>
            </template>

            <span style="display: flex;">
              <Button size="small" ghost @click="onShowTakeoffToPointPopover" style="margin-top:10px">
                <span>起飞</span>
              </Button>
              <div v-for="(cmdItem) in cmdList" :key="cmdItem.cmdKey" class="control-cmd-item" style="margin-top: 10px;">
                <Button :loading="cmdItem.loading" size="small" ghost @click="sendControlCmd(cmdItem, 0)">
                  {{ cmdItem.operateText }}
                </Button>
              </div>
            </span>

            <span style="display: flex;">
              <div>
                <!-- <Button size="small" ghost @click="openLivestreamAgora">
                  <span>Agora Live</span>
                </Button> -->
                <Button size="small" ghost @click="openLivestreamOthers" style="margin-top:10px">
                  <span>直播</span>
                </Button>
              </div>
              <div>
                <Button size="small" ghost @click="flighttask('暂停')" style="margin-top:10px">
                  <span>暂停航线</span>
                </Button>
                <Button size="small" ghost @click="flighttask('恢复')" style="margin-top:10px">
                  <span>恢复航线</span>
                </Button>
              </div>
            </span>
          </DroneControlPopover>
        </div>
      </div>

      <div class="box">
        <div class="row">
          <Select v-model:value="payloadSelectInfo.value" style="width: 110px; marginRight: 5px" :options="payloadSelectInfo.options" @change="handlePayloadChange"/>
          <div class="drone-control">
            <Button type="primary" size="small" @click="onAuthPayload">负载控制</Button>
          </div>
        </div>
        <div class="row">
          <DroneControlPopover
              :visible="gimbalResetPopoverData.visible"
              :loading="gimbalResetPopoverData.loading"
              @confirm="($event) => onGimbalResetConfirm(true)"
              @cancel="($event) => onGimbalResetConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">reset mode:</span>
                  <a-select
                      v-model:value="gimbalResetPopoverData.resetMode"
                      style="width: 180px"
                      :options="GimbalResetModeOptions"
                  ></a-select>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="onShowGimbalResetPopover">
              <span>云台重置</span>
            </Button>
          </DroneControlPopover>
          <DroneControlPopover
              :visible="switchCameraModePopoverData.visible"
              :loading="switchCameraModePopoverData.loading"
              @confirm="($event) => onSwitchCameraModeConfirm(true)"
              @cancel="($event) => onSwitchCameraModeConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机模式:</span>
                  <a-select
                      v-model:value="switchCameraModePopoverData.cameraMode"
                      style="width: 120px"
                      :options="CameraModeOptions"
                  ></a-select>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="onShowSwitchCameraModePopover">
              <span class="word">切换相机模式</span>
            </Button>
          </DroneControlPopover>
        </div>
        <div class="row">
          <Button size="small" ghost @click="onStartCameraRecording">
            <span>开始录像</span>
          </Button>
          <Button size="small" ghost @click="onStopCameraRecording">
            <span>结束录像</span>
          </Button>
        </div>
        <div class="row">
          <Button size="small" ghost @click="onTakeCameraPhoto">
            <span>拍照</span>
          </Button>
          <DroneControlPopover
              :visible="zoomFactorPopoverData.visible"
              :loading="zoomFactorPopoverData.loading"
              @confirm="($event) => onZoomFactorConfirm(true)"
              @cancel="($event) => onZoomFactorConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机类型:</span>
                  <a-select
                      v-model:value="zoomFactorPopoverData.cameraType"
                      style="width: 120px"
                      :options="CameraTypeOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">变焦倍数:</span>
                  <el-input-number v-if="zoomFactorPopoverData.cameraType == 'ir'" v-model="zoomFactorPopoverData.zoomFactor" :min="2" :max="20" :precision="2"/>
                  <el-input-number v-else v-model="zoomFactorPopoverData.zoomFactor" :min="2" :max="200" :precision="2"/>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowZoomFactorPopover()">
              <span class="word">变焦</span>
            </Button>
          </DroneControlPopover>
          <DroneControlPopover
              :visible="cameraAimPopoverData.visible"
              :loading="cameraAimPopoverData.loading"
              @confirm="($event) => onCameraAimConfirm(true)"
              @cancel="($event) =>onCameraAimConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机类型:</span>
                  <a-select
                      v-model:value="cameraAimPopoverData.cameraType"
                      style="width: 120px"
                      :options="CameraTypeOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">锁定:</span>
                  <a-switch v-model:checked="cameraAimPopoverData.locked"/>
                </div>
                <div>
                  <span class="form-label">x:</span>
                  <el-input-number v-model="cameraAimPopoverData.x" :min="0" :max="1" :precision="2"/>
                </div>
                <div>
                  <span class="form-label">y:</span>
                  <el-input-number v-model="cameraAimPopoverData.y" :min="0" :max="1" :precision="2"/>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowCameraAimPopover()">
              <span class="word">镜头中心</span>
            </Button>
          </DroneControlPopover>
        </div>
        <div class="row">
          <DroneControlPopover
              :visible="cameraExposurePopoverData.visible"
              :loading="cameraExposurePopoverData.loading"
              @confirm="($event) => onCameraExposureConfirm(true)"
              @cancel="($event) => onCameraExposureConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机类型:</span>
                  <a-select
                      v-model:value="cameraExposurePopoverData.cameraType"
                      style="width: 120px"
                      :options="CameraTypesOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">相机模式:</span>
                  <a-select
                      v-model:value="cameraExposurePopoverData.cameraMode"
                      style="width: 120px"
                      :options="CameraExposureModeOptions"
                  ></a-select>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowCameraExposurePopover()">
              <span class="word">相机曝光模式</span>
            </Button>
          </DroneControlPopover>
          <DroneControlPopover
              :visible="cameraExposureValuePopoverData.visible"
              :loading="cameraExposureValuePopoverData.loading"
              @confirm="($event) => onCameraExposureValueConfirm(true)"
              @cancel="($event) => onCameraExposureValueConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机类型:</span>
                  <a-select
                      v-model:value="cameraExposureValuePopoverData.cameraType"
                      style="width: 120px"
                      :options="CameraTypesOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">曝光值:</span>
                  <a-select
                      v-model:value="cameraExposureValuePopoverData.cameraValue"
                      style="width: 120px"
                      :options="CameraExposureValueOptions"
                  ></a-select>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowCameraExposureValuePopover()">
              <span class="word">曝光值调节</span>
            </Button>
          </DroneControlPopover>
        </div>
        <div class="row">
          <DroneControlPopover
              :visible="cameraFocusPopoverData.visible"
              :loading="cameraFocusPopoverData.loading"
              @confirm="($event) => onCameraFocusConfirm(true)"
              @cancel="($event) => onCameraFocusConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机类型:</span>
                  <a-select
                      v-model:value="cameraFocusPopoverData.cameraType"
                      style="width: 120px"
                      :options="CameraTypesOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">对焦模式:</span>
                  <a-select
                      v-model:value="cameraFocusPopoverData.cameraMode"
                      style="width: 120px"
                      :options="CameraFocusModeOptions"
                  ></a-select>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowCameraFocusPopover()">
              <span class="word">相机对焦模式</span>
            </Button>
          </DroneControlPopover>
          <DroneControlPopover
              :visible="cameraFocusValuePopoverData.visible"
              :loading="cameraFocusValuePopoverData.loading"
              @confirm="($event) => onCameraFocusValueConfirm(true)"
              @cancel="($event) => onCameraFocusValueConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">相机类型:</span>
                  <a-select
                      v-model:value="cameraFocusValuePopoverData.cameraType"
                      style="width: 120px"
                      :options="CameraTypesOptions"
                  ></a-select>
                </div>
                <div>
                  <span class="form-label">对焦值:</span>
                  <a-input-number v-model:value="cameraFocusValuePopoverData.cameraValue" :min="0" :max="50"/>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowCameraFocusValuePopover()">
              <span class="word">对焦值调节</span>
            </Button>
          </DroneControlPopover>
        </div>
        <div class="row">
          <DroneControlPopover
              :visible="thermometricPopoverData.visible"
              :loading="thermometricPopoverData.loading"
              @confirm="($event) => onThermometricConfirm(true)"
              @cancel="($event) => onThermometricConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">测温类型:</span>
                  <a-select
                      v-model:value="thermometricPopoverData.thermometricType"
                      style="width: 120px"
                      :options="ThermometricTypesOptions"
                  ></a-select>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowThermometricPopover()">
              <span class="word">测温模式</span>
            </Button>
          </DroneControlPopover>
          <DroneControlPopover
              :visible="thermometricPointPopoverData.visible"
              :loading="thermometricPointPopoverData.loading"
              @confirm="($event) => onThermometricPointConfirm(true)"
              @cancel="($event) => onThermometricPointConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">x:</span>
                  <el-input-number v-model="thermometricPointPopoverData.x" :min="0" :max="1" :precision="2"/>
                </div>
                <div>
                  <span class="form-label">y:</span>
                  <el-input-number v-model="thermometricPointPopoverData.y" :min="0" :max="1" :precision="2"/>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowThermometricPointPopover()">
              <span class="word">测温点</span>
            </Button>
          </DroneControlPopover>
          <DroneControlPopover
              :visible="thermometricAreaPopoverData.visible"
              :loading="thermometricAreaPopoverData.loading"
              @confirm="($event) => onThermometricAreaConfirm(true)"
              @cancel="($event) => onThermometricAreaConfirm(false)"
          >
            <template #formContent>
              <div class="form-content">
                <div>
                  <span class="form-label">x:</span>
                  <el-input-number v-model="thermometricAreaPopoverData.x" :min="0" :max="1" :precision="2"/>
                </div>
                <div>
                  <span class="form-label">y:</span>
                  <el-input-number v-model="thermometricAreaPopoverData.y" :min="0" :max="1" :precision="2"/>
                </div>
                <div>
                  <span class="form-label">宽度:</span>
                  <el-input-number v-model="thermometricAreaPopoverData.width" :min="0" :max="1" :precision="2"/>
                </div>
                <div>
                  <span class="form-label">高度:</span>
                  <el-input-number v-model="thermometricAreaPopoverData.height" :min="0" :max="1" :precision="2"/>
                </div>
              </div>
            </template>
            <Button size="small" ghost @click="() => onShowThermometricAreaPopover()">
              <span class="word">测温区域</span>
            </Button>
          </DroneControlPopover>
        </div>
      </div>
    </div>

    <!-- 信息提示 -->
    <!--<DroneControlInfoPanel :message="drcInfo"></DroneControlInfoPanel>-->
  </div>
</template>

<script setup lang="ts">
import {defineProps, reactive, ref, watch, computed} from 'vue'
import {Select, message, Button} from 'ant-design-vue'
import {PayloadInfo, DeviceInfoType, ControlSource, DeviceOsdCamera} from '@/types/device'
import store from '@/store'
import {postDrcEnter, postDrcExit} from '@/api/drc'
import {useMqtt, DeviceTopicInfo} from './use-mqtt'
import {DownOutlined, UpOutlined, LeftOutlined, RightOutlined, PauseCircleOutlined, UndoOutlined, RedoOutlined, ArrowUpOutlined, ArrowDownOutlined} from '@ant-design/icons-vue'
import {useManualControl, KeyCode} from './use-manual-control_new'
import {usePayloadControl} from './use-payload-control'
import {
  CameraMode, CameraType, CameraTypeOptions, CameraModeOptions, CameraListItem, CameraExposureModeOptions,
  CameraTypesOptions, CameraExposureValueOptions, CameraFocusModeOptions, ThermometricTypesOptions
} from '@/types/live-stream'
import {useDroneControlWsEvent} from './use-drone-control-ws-event'
import {useDroneControlMqttEvent} from './use-drone-control-mqtt-event'
import {
  postFlightAuth, LostControlActionInCommandFLight, WaylineLostControlActionInCommandFlight, ERthMode,
  ECommanderModeLostAction, ECommanderFlightMode
} from '@/api/drone-control/drone'
import {useDroneControl} from './use-drone-control'
import {
  GimbalResetMode, GimbalResetModeOptions, LostControlActionInCommandFLightOptions, WaylineLostControlActionInCommandFlightOptions,
  RthModeInCommandFlightOptions, CommanderModeLostActionInCommandFlightOptions, CommanderFlightModeInCommandFlightOptions
} from '@/types/drone-control'
import DroneControlPopover from './DroneControlPopover.vue'
import DroneControlInfoPanel from './DroneControlInfoPanel.vue'
import {noDebugCmdList as baseCmdList, DeviceCmdItem} from '@/types/device-cmd'
import {useDockControl} from './use-dock-control'
import {ELocalStorageKey} from '@/api/enum/index'
import {flighttaskPause, flighttaskRecovery} from '@/api/wayline'
import { getWorkspaceId } from '@/utils/storage'

const props = defineProps<{
  sn: string,
  deviceInfo: DeviceInfoType,
  payloads: null | PayloadInfo[]
}>()


const clientId = computed(() => {
  return store.state.dock.clientId
})

const initCmdList = baseCmdList.map(cmdItem => Object.assign({}, cmdItem))
const cmdList = ref(initCmdList)

const {sendDockControlCmd} = useDockControl()

async function sendControlCmd(cmdItem: DeviceCmdItem, index: number) {
  cmdItem.loading = true
  const result = await sendDockControlCmd({
    sn: props.sn,
    cmd: cmdItem.cmdKey,
    action: cmdItem.action
  }, false)
  if (result) {
    message.success('Return home successful')
    if (flightController.value) {
      exitFlightCOntrol()
    }
  } else {
    message.error('Failed to return home')
  }
  cmdItem.loading = false
}

const {flyToPoint, stopFlyToPoint, takeoffToPoint} = useDroneControl()
const MAX_SPEED = 14

const flyToPointPopoverData = reactive({
  visible: false,
  loading: false,
  latitude: null as null | number,
  longitude: null as null | number,
  height: null as null | number,
  maxSpeed: MAX_SPEED,
})

function onShowFlyToPopover() {
  flyToPointPopoverData.visible = !flyToPointPopoverData.visible
  flyToPointPopoverData.loading = false
  flyToPointPopoverData.latitude = null
  flyToPointPopoverData.longitude = null
  flyToPointPopoverData.height = null
}

async function onFlyToConfirm(confirm: boolean) {
  if (confirm) {
    if (!flyToPointPopoverData.height || !flyToPointPopoverData.latitude || !flyToPointPopoverData.longitude) {
      message.error('Input error')
      return
    }
    try {
      await flyToPoint(props.sn, {
        max_speed: flyToPointPopoverData.maxSpeed,
        points: [
          {
            latitude: flyToPointPopoverData.latitude,
            longitude: flyToPointPopoverData.longitude,
            height: flyToPointPopoverData.height
          }
        ]
      })
    } catch (error) {
    }
  }
  flyToPointPopoverData.visible = false
}

async function onStopFlyToPoint() {
  await stopFlyToPoint(props.sn)
}

const takeoffToPointPopoverData = reactive({
  visible: false,
  loading: false,
  latitude: null as null | number,
  longitude: null as null | number,
  height: null as null | number,
  securityTakeoffHeight: null as null | number,
  maxSpeed: MAX_SPEED,
  rthAltitude: null as null | number,
  rcLostAction: LostControlActionInCommandFLight.RETURN_HOME,
  exitWaylineWhenRcLost: WaylineLostControlActionInCommandFlight.EXEC_LOST_ACTION,
  rthMode: ERthMode.SETTING,
  commanderModeLostAction: ECommanderModeLostAction.CONTINUE,
  commanderFlightMode: ECommanderFlightMode.SETTING,
  commanderFlightHeight: null as null | number,
})

function onShowTakeoffToPointPopover() {
  takeoffToPointPopoverData.visible = !takeoffToPointPopoverData.visible
  takeoffToPointPopoverData.loading = false
  takeoffToPointPopoverData.latitude = null
  takeoffToPointPopoverData.longitude = null
  takeoffToPointPopoverData.securityTakeoffHeight = null
  takeoffToPointPopoverData.rthAltitude = null
  takeoffToPointPopoverData.rcLostAction = LostControlActionInCommandFLight.RETURN_HOME
  takeoffToPointPopoverData.exitWaylineWhenRcLost = WaylineLostControlActionInCommandFlight.EXEC_LOST_ACTION
  takeoffToPointPopoverData.rthMode = ERthMode.SETTING
  takeoffToPointPopoverData.commanderModeLostAction = ECommanderModeLostAction.CONTINUE
  takeoffToPointPopoverData.commanderFlightMode = ECommanderFlightMode.SETTING
  takeoffToPointPopoverData.commanderFlightHeight = null
}

async function onTakeoffToPointConfirm(confirm: boolean) {
  if (confirm) {
    if (!takeoffToPointPopoverData.height ||
        !takeoffToPointPopoverData.latitude ||
        !takeoffToPointPopoverData.longitude ||
        !takeoffToPointPopoverData.securityTakeoffHeight ||
        !takeoffToPointPopoverData.rthAltitude ||
        !takeoffToPointPopoverData.commanderFlightHeight) {
      message.error('Input error')
      return
    }
    try {
      await takeoffToPoint(props.sn, {
        target_latitude: takeoffToPointPopoverData.latitude,
        target_longitude: takeoffToPointPopoverData.longitude,
        target_height: takeoffToPointPopoverData.height,
        security_takeoff_height: takeoffToPointPopoverData.securityTakeoffHeight,
        rth_altitude: takeoffToPointPopoverData.rthAltitude,
        max_speed: takeoffToPointPopoverData.maxSpeed,
        rc_lost_action: takeoffToPointPopoverData.rcLostAction,
        exit_wayline_when_rc_lost: takeoffToPointPopoverData.exitWaylineWhenRcLost,
        rth_mode: takeoffToPointPopoverData.rthMode,
        commander_mode_lost_action: takeoffToPointPopoverData.commanderModeLostAction,
        commander_flight_mode: takeoffToPointPopoverData.commanderFlightMode,
        commander_flight_height: takeoffToPointPopoverData.commanderFlightHeight,
      })
    } catch (error) {
    }
  }
  takeoffToPointPopoverData.visible = false
}

const deviceTopicInfo: DeviceTopicInfo = reactive({
  sn: props.sn,
  pubTopic: '',
  subTopic: ''
})

useMqtt(deviceTopicInfo)
const flightController = ref(false)

/* async function onClickFightControl() {
  if (flightController.value) {
    exitFlightCOntrol()
    return
  }
  enterFlightControl()
}
async function onClickFightControl() {
  if (flightController.value) {
    exitFlightCOntrol()
    return
  }
  enterFlightControl()
} */

// 进入飞行控制
const isHeartbeatActive = ref(false);

async function enterFlightControl() {
  try {
    const {code, data} = await postDrcEnter({
      client_id: clientId.value,
      dock_sn: props.sn,
    })
    if (code === 0) {
      flightController.value = true
      if (data.sub && data.sub.length > 0) {
        deviceTopicInfo.subTopic = data.sub[0]
      }
      if (data.pub && data.pub.length > 0) {
        deviceTopicInfo.pubTopic = data.pub[0]
      }
      // 获取飞行控制权
      if (droneControlSource.value !== ControlSource.A) {
        await postFlightAuth(props.sn)
      }
      console.log('deviceTopicInfo', deviceTopicInfo)
      const mqtt = useMqtt(deviceTopicInfo);
      isHeartbeatActive.value = mqtt.isHeartbeatActive;
      message.success('Get flight control successfully')
    }
  } catch (error: any) {
  }
}

// 退出飞行控制
async function exitFlightCOntrol() {
  try {
    await postDrcExit({
      client_id: clientId.value,
      dock_sn: props.sn,
    })
    flightController.value = false
    deviceTopicInfo.subTopic = ''
    deviceTopicInfo.pubTopic = ''
    message.success('Exit flight control')
  } catch (error: any) {
    console.log(error)
  }
}

// drc mqtt message
const {drcInfo, errorInfo} = useDroneControlMqttEvent(props.sn)

const {
  handleKeyup,
  handleEmergencyStop,
  resetControlState,
} = useManualControl(deviceTopicInfo, flightController)

function onMouseDown(type: KeyCode) {
  handleKeyup(type)
}

function onMouseUp(type: KeyCode) {
  resetControlState(type)
}

// 负载控制
const payloadSelectInfo = {
  value: null as any,
  controlSource: undefined as undefined | ControlSource,
  options: [] as any,
  payloadIndex: '' as string,
  camera: undefined as undefined | DeviceOsdCamera // 当前负载osd信息
}

const handlePayloadChange = (value: string) => {
  const payload = props.payloads?.find(item => item.payload_sn === value)
  if (payload) {
    payloadSelectInfo.payloadIndex = payload.payload_index || ''
    payloadSelectInfo.controlSource = payload.control_source
    payloadSelectInfo.camera = undefined
  }
}

// function getCurrentCamera (cameraList: CameraListItem[], cameraIndex?: string):CameraListItem | null {
//   let camera = null
//   cameraList.forEach(item => {
//     if (item.camera_index === cameraIndex) {
//       camera = item
//     }
//   })
//   return camera
// }

// const currentCamera = computed(() => {
//   return getCurrentCamera(props.deviceInfo.dock.basic_osd.live_capacity?.device_list[0]?.camera_list as CameraListItem[], camera_index)
// })

// 更新负载信息
watch(() => props.payloads, (payloads) => {
  if (payloads && payloads.length > 0) {
    payloadSelectInfo.value = payloads[0].payload_sn
    payloadSelectInfo.controlSource = payloads[0].control_source || ControlSource.B
    payloadSelectInfo.payloadIndex = payloads[0].payload_index || ''
    payloadSelectInfo.options = payloads.map(item => ({label: item.payload_name, value: item.payload_sn}))
    payloadSelectInfo.camera = undefined
  } else {
    payloadSelectInfo.value = null
    payloadSelectInfo.controlSource = undefined
    payloadSelectInfo.options = []
    payloadSelectInfo.payloadIndex = ''
    payloadSelectInfo.camera = undefined
  }
}, {
  immediate: true,
  deep: true
})

watch(() => props.deviceInfo.device, (droneOsd) => {
  if (droneOsd && droneOsd.cameras) {
    payloadSelectInfo.camera = droneOsd.cameras.find(item => item.payload_index === payloadSelectInfo.payloadIndex)
  } else {
    payloadSelectInfo.camera = undefined
  }
}, {
  immediate: true,
  deep: true
})

// ws 消息通知
const {droneControlSource, payloadControlSource} = useDroneControlWsEvent(props.sn, payloadSelectInfo.value)
watch(() => payloadControlSource, (controlSource) => {
  payloadSelectInfo.controlSource = controlSource.value
}, {
  immediate: true,
  deep: true
})

// watch(isHeartbeatActive, (value) => {
//   console.log('Heartbeat Active:', value)
// })

const {
  checkPayloadAuth,
  authPayload,
  resetGimbal,
  switchCameraMode,
  takeCameraPhoto,
  startCameraRecording,
  stopCameraRecording,
  changeCameraFocalLength,
  cameraAim,
  cameraExposure,
  cameraExposureValue,
  cameraFocus,
  cameraFocusValue,
  thermometric,
  thermometricPoint,
  thermometricArea,
} = usePayloadControl()

async function onAuthPayload() {
  const result = await authPayload(props.sn, payloadSelectInfo.payloadIndex)
  if (result) {
    payloadControlSource.value = ControlSource.A
  }
}

const gimbalResetPopoverData = reactive({
  visible: false,
  loading: false,
  resetMode: null as null | GimbalResetMode,
})

function onShowGimbalResetPopover() {
  gimbalResetPopoverData.visible = !gimbalResetPopoverData.visible
  gimbalResetPopoverData.loading = false
  gimbalResetPopoverData.resetMode = null
}

async function onGimbalResetConfirm(confirm: boolean) {
  if (confirm) {
    if (gimbalResetPopoverData.resetMode === null) {
      message.error('Please select reset mode')
      return
    }
    gimbalResetPopoverData.loading = true
    try {
      await resetGimbal(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        reset_mode: gimbalResetPopoverData.resetMode
      })
    } catch (err) {
    }
  }
  gimbalResetPopoverData.visible = false
}

async function onTakeCameraPhoto() {
  if (!checkPayloadAuth(payloadSelectInfo.controlSource)) {
    return
  }
  await takeCameraPhoto(props.sn, payloadSelectInfo.payloadIndex)
}

async function onStartCameraRecording() {
  if (!checkPayloadAuth(payloadSelectInfo.controlSource)) {
    return
  }
  await startCameraRecording(props.sn, payloadSelectInfo.payloadIndex)
}

async function onStopCameraRecording() {
  if (!checkPayloadAuth(payloadSelectInfo.controlSource)) {
    return
  }
  await stopCameraRecording(props.sn, payloadSelectInfo.payloadIndex)
}

function openLivestreamOthers() {
  store.commit('SET_LIVESTREAM_OTHERS_VISIBLE', true)
}

function openLivestreamAgora() {
  store.commit('SET_LIVESTREAM_AGORA_VISIBLE', true)
}

watch(() => errorInfo, (errorInfo) => {
  if (errorInfo.value) {
    message.error(errorInfo.value)
    errorInfo.value = ''
  }
}, {
  immediate: true,
  deep: true
})

// 拍照
const zoomFactorPopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  zoomFactor: null as null | number,
})

function onShowZoomFactorPopover() {
  if (payloadSelectInfo.value) {
    zoomFactorPopoverData.visible = !zoomFactorPopoverData.visible
    zoomFactorPopoverData.loading = false
    zoomFactorPopoverData.cameraType = null
    zoomFactorPopoverData.zoomFactor = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onZoomFactorConfirm(confirm: boolean) {
  if (confirm) {
    if (!zoomFactorPopoverData.zoomFactor || zoomFactorPopoverData.cameraType === null) {
      message.error('Please input Zoom Factor')
      return
    }
    zoomFactorPopoverData.loading = true
    try {
      await changeCameraFocalLength(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: zoomFactorPopoverData.cameraType,
        zoom_factor: zoomFactorPopoverData.zoomFactor
      })
    } catch (err) {
    }
  }
  zoomFactorPopoverData.visible = false
}

// 变焦
const cameraAimPopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  locked: false,
  x: null as null | number,
  y: null as null | number,
})

function onShowCameraAimPopover() {
  if (payloadSelectInfo.value) {
    cameraAimPopoverData.visible = !cameraAimPopoverData.visible
    cameraAimPopoverData.loading = false
    cameraAimPopoverData.cameraType = null
    cameraAimPopoverData.locked = false
    cameraAimPopoverData.x = null
    cameraAimPopoverData.y = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onCameraAimConfirm(confirm: boolean) {
  if (confirm) {
    if (cameraAimPopoverData.cameraType === null || cameraAimPopoverData.x === null || cameraAimPopoverData.y === null) {
      message.error('Input error')
      return
    }
    try {
      await cameraAim(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraAimPopoverData.cameraType,
        locked: cameraAimPopoverData.locked,
        x: cameraAimPopoverData.x,
        y: cameraAimPopoverData.y,
      })
    } catch (error) {
    }
  }
  cameraAimPopoverData.visible = false
}

// 切换相机模式
const switchCameraModePopoverData = reactive({
  visible: false,
  loading: false,
  cameraMode: null as null | CameraMode,
})

function onShowSwitchCameraModePopover() {
  if (payloadSelectInfo.value) {
    switchCameraModePopoverData.visible = !switchCameraModePopoverData.visible
    switchCameraModePopoverData.loading = false
    switchCameraModePopoverData.cameraMode = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onSwitchCameraModeConfirm(confirm: boolean) {
  if (confirm) {
    if (switchCameraModePopoverData.cameraMode === null) {
      message.error('Input error')
      return
    }
    try {
      await switchCameraMode(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_mode: switchCameraModePopoverData.cameraMode,
      })
    } catch (error) {
      console.log(error)
    }
  }
  switchCameraModePopoverData.visible = false
}

// 相机曝光模式
const cameraExposurePopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraMode: null as null | CameraMode,
})

function onShowCameraExposurePopover() {
  if (payloadSelectInfo.value) {
    cameraExposurePopoverData.visible = !cameraExposurePopoverData.visible
    cameraExposurePopoverData.loading = false
    cameraExposurePopoverData.cameraType = null
    cameraExposurePopoverData.cameraMode = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onCameraExposureConfirm(confirm: boolean) {
  if (confirm) {
    if (cameraExposurePopoverData.cameraType === null || cameraExposurePopoverData.cameraMode === null) {
      message.error('Input error')
      return
    }
    try {
      await cameraExposure(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraExposurePopoverData.cameraType,
        exposure_mode: cameraExposurePopoverData.cameraMode,
      })
    } catch (error) {
      console.log(error)
    }
  }
  cameraExposurePopoverData.visible = false
}

// 曝光值调节
const cameraExposureValuePopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraValue: null as null,
})

function onShowCameraExposureValuePopover() {
  if (payloadSelectInfo.value) {
    cameraExposureValuePopoverData.visible = !cameraExposurePopoverData.visible
    cameraExposureValuePopoverData.loading = false
    cameraExposureValuePopoverData.cameraType = null
    cameraExposureValuePopoverData.cameraValue = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onCameraExposureValueConfirm(confirm: boolean) {
  if (confirm) {
    if (cameraExposureValuePopoverData.cameraType === null || cameraExposureValuePopoverData.cameraValue === null) {
      message.error('Input error')
      return
    }
    try {
      await cameraExposureValue(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraExposureValuePopoverData.cameraType,
        exposure_value: cameraExposureValuePopoverData.cameraValue,
      })
    } catch (error) {
      console.log(error)
    }
  }
  cameraExposureValuePopoverData.visible = false
}

// 相机对焦模式
const cameraFocusPopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraMode: null as null | CameraMode,
})

function onShowCameraFocusPopover() {
  if (payloadSelectInfo.value) {
    cameraFocusPopoverData.visible = !cameraFocusPopoverData.visible
    cameraFocusPopoverData.loading = false
    cameraFocusPopoverData.cameraType = null
    cameraFocusPopoverData.cameraMode = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onCameraFocusConfirm(confirm: boolean) {
  if (confirm) {
    if (cameraFocusPopoverData.cameraType === null || cameraFocusPopoverData.cameraMode === null) {
      message.error('Input error')
      return
    }
    try {
      await cameraFocus(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraFocusPopoverData.cameraType,
        focus_mode: cameraFocusPopoverData.cameraMode,
      })
    } catch (error) {
      console.log(error)
    }
  }
  cameraFocusPopoverData.visible = false
}

// 对焦值调节
const cameraFocusValuePopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraValue: null as null,
})

function onShowCameraFocusValuePopover() {
  if (payloadSelectInfo.value) {
    cameraFocusValuePopoverData.visible = !cameraFocusValuePopoverData.visible
    cameraFocusValuePopoverData.loading = false
    cameraFocusValuePopoverData.cameraType = null
    cameraFocusValuePopoverData.cameraValue = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onCameraFocusValueConfirm(confirm: boolean) {
  if (confirm) {
    if (cameraFocusValuePopoverData.cameraType === null || cameraFocusValuePopoverData.cameraValue === null) {
      message.error('Input error')
      return
    }
    try {
      await cameraFocusValue(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraFocusValuePopoverData.cameraType,
        focus_value: cameraFocusValuePopoverData.cameraValue,
      })
    } catch (error) {
      console.log(error)
    }
  }
  cameraFocusValuePopoverData.visible = false
}

// 测温模式
const thermometricPopoverData = reactive({
  visible: false,
  loading: false,
  thermometricType: null,
})

function onShowThermometricPopover() {
  if (payloadSelectInfo.value) {
    thermometricPopoverData.visible = !thermometricPopoverData.visible
    thermometricPopoverData.loading = false
    thermometricPopoverData.thermometricType = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onThermometricConfirm(confirm: boolean) {
  if (confirm) {
    if (thermometricPopoverData.thermometricType === null) {
      message.error('Input error')
      return
    }
    try {
      await thermometric(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        mode: thermometricPopoverData.thermometricType,
      })
    } catch (error) {
      console.log(error)
    }
  }
  thermometricPopoverData.visible = false
}

// 测温点
const thermometricPointPopoverData = reactive({
  visible: false,
  loading: false,
  x: null as null | number,
  y: null as null | number,
})

function onShowThermometricPointPopover() {
  if (payloadSelectInfo.value) {
    thermometricPointPopoverData.visible = !thermometricPointPopoverData.visible
    thermometricPointPopoverData.loading = false
    thermometricPointPopoverData.x = null
    thermometricPointPopoverData.y = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onThermometricPointConfirm(confirm: boolean) {
  if (confirm) {
    if (thermometricPointPopoverData.x === null || thermometricPointPopoverData.y === null) {
      message.error('Input error')
      return
    }
    try {
      await thermometricPoint(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        x: thermometricPointPopoverData.x,
        y: thermometricPointPopoverData.y,
      })
    } catch (error) {
      console.log(error)
    }
  }
  thermometricPointPopoverData.visible = false
}

// 测温区域
const thermometricAreaPopoverData = reactive({
  visible: false,
  loading: false,
  x: null as null | number,
  y: null as null | number,
  width: null as null | number,
  height: null as null | number,
})

function onShowThermometricAreaPopover() {
  if (payloadSelectInfo.value) {
    thermometricAreaPopoverData.visible = !thermometricAreaPopoverData.visible
    thermometricAreaPopoverData.loading = false
    thermometricAreaPopoverData.x = null
    thermometricAreaPopoverData.y = null
    thermometricAreaPopoverData.width = null
    thermometricAreaPopoverData.height = null
  } else {
    message.warning('请先选择负载！')
  }
}

async function onThermometricAreaConfirm(confirm: boolean) {
  if (confirm) {
    if (thermometricAreaPopoverData.x === null || thermometricAreaPopoverData.y === thermometricAreaPopoverData.width === null || thermometricAreaPopoverData.height === null) {
      message.error('Input error')
      return
    }
    try {
      await thermometricArea(props.sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        x: thermometricAreaPopoverData.x,
        y: thermometricAreaPopoverData.y,
        width: thermometricAreaPopoverData.width,
        height: thermometricAreaPopoverData.height,
      })
    } catch (error) {
      console.log(error)
    }
  }
  thermometricAreaPopoverData.visible = false
}

function flighttask(type) {
  let gateWaySn = store.state.dock.osdVisible.gateway_sn;
  if (type == '暂停') {
    flighttaskPause(getWorkspaceId(), gateWaySn).then(res => {
      if (res.code === 0) {
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    });
  } else {
    flighttaskRecovery(getWorkspaceId(), gateWaySn).then(res => {
      if (res.code === 0) {
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    })
  }
}
</script>

<style lang='scss' scoped>
.drone-control-wrapper {
  .drone-control-header {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 10px 0;
  }

  .drone-control-box {
    display: flex;
    flex-wrap: wrap;

    .box {
      width: 50%;
      padding: 5px;
      border: 0.5px solid rgba(255, 255, 255, 0.3);
      height: 142px;
      overflow-y: scroll;

      .row {
        display: flex;
        flex-wrap: wrap;
        padding: 2px;

        + .row {
          margin-bottom: 6px;
        }

        &::v-deep {
          .ant-btn {
            font-size: 12px;
            padding: 0 4px;
            margin-right: 5px;
          }
        }
      }

      .drone-control {
        &::v-deep {
          .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
            padding: 0 2px;
          }
        }
      }

      .drone-control-direction {
        margin-right: 10px;

        .ant-btn {
          margin-right: 0;
        }

        .word {
          width: 12px;
          margin-left: 2px;
          font-size: 12px;
          color: #aaa;
        }
      }
    }
  }
}

.active_circle {
  display: inline-flex;
  margin-left: 10px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
</style>