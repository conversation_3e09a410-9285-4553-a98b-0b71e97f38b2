<template>
    <div class="g-map-wrapper">
      <!-- 地图区域 -->
      <div id="g-container"></div>
      <!-- 绘制面板 -->
      <div class="g-action-panel">
        <!-- <div
          :class="state.currentType === 'pin' ? 'g-action-item selection' : 'g-action-item'"
          @click="draw('pin', true)"
        >
          <a><a-image :src="pin" :preview="false" /></a>
        </div> -->
        <div :class="state.currentType === 'polyline' ? 'g-action-item selection' : 'g-action-item'"
          @click="draw('polyline', true)" v-if="layerMapType">
          <a-tooltip placement="left">
            <template #title>
              <span>鼠标左键点击画线，右键保存</span>
            </template>
            <a>
              <LineOutlined :rotate="135" class="iconBgcode fz20" />
            </a>
          </a-tooltip>
        </div>
        <div :class="state.currentType === 'polygon' && !state.isFlightArea
          ? 'g-action-item selection'
          : 'g-action-item'
          " @click="draw('polygon', true)" v-if="layerMapType">
          <a-tooltip placement="left">
            <template #title>
              <span>鼠标左键点击画多边形，右键保存</span>
            </template>
            <a>
              <BorderOutlined class="iconBgcode  fz18" />
            </a>
          </a-tooltip>
        </div>
        <a-tooltip placement="left">
          <template #title>
            <span>鼠标左键点击画圆或多边形，右键保存</span>
          </template>
          <FlightAreaActionIcon v-if="mapType" class="g-action-item mt10"
            :class="{ selection: mouseMode && state.isFlightArea }" @select-action="selectFlightAreaAction"
            @click="selectFlightAreaAction" />
        </a-tooltip>
  
        <div v-if="mouseMode" class="g-action-item" @click="draw('off', false)">
          <a>
            <CloseOutlined style="color: red" />
          </a>
        </div>
        <div v-if="layerMapType || mapType">
          <div v-if="eyeShow" class="g-action-item" @click="hideShow(1)">
            <a class="iconBgcode">
              <EyeOutlined style="margin-top: 14%;width:18px;" />
            </a>
          </div>
          <div class="g-action-item" @click="hideShow(2)" v-else>
            <a style="color:#dadada;">
              <EyeInvisibleOutlined style="margin-top: 14%;width:18px;" />
            </a>
          </div>
        </div>
        <div class="g-action-item">
          <a-popover v-if="droneList.length > 1" placement="leftTop" trigger="click" v-model:visible="droneListVisible"
            :overlayStyle="{ width: '250px' }">
            <template #title>
              <div class="drone-list-title">
                <span>无人机</span>
                <a-button danger type="text" @click="selectDroneToFollow('')">取消跟随</a-button>
              </div>
            </template>
            <template #content>
              <div class="drone-list">
                <div v-if="droneList.length === 0" class="empty-list">
                  暂无在线无人机
                </div>
                <div v-for="drone in droneList" :key="drone.sn" class="drone-item"
                  :class="{ active: followingSn === drone.sn && cameraFollow }" @click="selectDroneToFollow(drone.sn)">
                  <div class="drone-info">
                    <div class="drone-name">{{ drone.model || `无人机 ${drone.sn.substring(0, 6)}` }}</div>
                    <div class="drone-sn">{{ drone.callsign }}</div>
                  </div>
                  <CheckOutlined v-if="followingSn === drone.sn && cameraFollow" class="follow-icon" />
                </div>
              </div>
            </template>
            <a>
              <AimOutlined :style="{ color: cameraFollow ? '#0aed8b' : '#dadada' }" />
            </a>
          </a-popover>
          <a-tooltip v-else placement="left">
            <template #title>
              <span>{{ droneList[0]?.callsign || `暂无在线无人机` }}</span>
            </template>
            <a @click="selectDroneToFollow(droneList[0]?.sn)">
              <AimOutlined :style="{ color: cameraFollow ? '#0aed8b' : '#dadada' }" />
            </a>
          </a-tooltip>
        </div>
        <div class="g-action-item" @click="toggleSceneMode">
          <a-tooltip placement="left">
            <template #title>
              <span>{{ getSceneModeText() }}</span>
            </template>
            <a>
              <GlobalOutlined :style="{ color: '#dadada' }" />
            </a>
          </a-tooltip>
        </div>
      </div>
      <div>
        <!-- 飞机OSD -->
        <div v-if="osdVisible.visible && !osdVisible.is_dock" v-drag-window class="osd-panel fz12">
          <div class="drag-title pl5 pr5 flex-align-center flex-row flex-justify-between"
            style="border-bottom: 1px solid #515151; height: 22%;">
            <span>{{ osdVisible.callsign }}</span>
  
          </div>
          <div class="drag-title pl5 pr5 flex-align-center flex-row flex-justify-between" style=" height: 18%;">
            <span><a class="fz16" style="color: white;position: absolute;right:10px;"
                @click.stop="() => osdVisible.visible = false">
                <CloseOutlined />
              </a></span>
          </div>
          <div style="height: 82%;">
            <div class="flex-column flex-align-center flex-justify-center"
              style="margin-top: -5px; padding-top: 25px; float: left; width: 60px; background: #2d2d2d;">
              <a-tooltip :title="osdVisible.model">
                <div style="width: 90%;" class="flex-column flex-align-center flex-justify-center">
                  <span><a-image :src="M30" :preview="false" /></span>
                  <span>{{ osdVisible.model }}</span>
                </div>
              </a-tooltip>
            </div>
            <div class="osd">
              <a-row>
                <a-col span="16"
                  :style="deviceInfo.device.mode_code === EModeCode.未连接 ? 'color: red; font-weight: 700;' : 'color: rgb(25,190,107)'">{{
                    EModeCode[deviceInfo.device.mode_code] }}</a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.hd">
                    <span>HD</span>
                    <span class="ml10">{{ deviceInfo.gateway?.transmission_signal_quality }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.rc_capacity_percent">
                    <span>
                      <ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14" />
                    </span>
                    <span class="ml10">{{ deviceInfo.gateway && deviceInfo.gateway.capacity_percent !== str ?
                      deviceInfo.gateway?.capacity_percent + ' %' : deviceInfo.gateway?.capacity_percent }}</span>
                  </a-tooltip>
                </a-col>
  
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.drone_capacity_percent">
                    <span>
                      <ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14" />
                    </span>
                    <span class="ml10">{{ deviceInfo.device.battery.capacity_percent !== str ?
                      deviceInfo.device.battery.capacity_percent + ' %' : deviceInfo.device.battery.capacity_percent
                    }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-tooltip :title="deviceTitle.rtk_fixed">
                  <a-col span="6" class="flex-row flex-align-center flex-justify-start">
                    <span>Fixed</span>
                    <span class="ml10 circle"
                      :style="deviceInfo.device.position_state.is_fixed === 1 ? 'backgroud: rgb(25,190,107);' : ' background: red;'"></span>
                  </a-col>
                </a-tooltip>
                <a-col span="6">
                  <a-tooltip title="GPS">
                    <span>GPS</span>
                    <span class="ml10">{{ deviceInfo.device.position_state.gps_number }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.rtk">
                    <span>
                      <TrademarkOutlined class="fz14" />
                    </span>
                    <span class="ml10">{{ deviceInfo.device.position_state.rtk_number }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.gear">
                    <span>
                      <ControlOutlined class="fz16" />
                    </span>
                    <span class="ml10">{{ EGear[deviceInfo?.device.gear] }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.Altitude">
                    <span>ASL</span>
                    <span class="ml10">{{ fixedNum('height') + ' m' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.takeoff">
                    <span>ALT</span>
                    <span class="ml10">{{ fixedNum('elevation') + ' m'
                    }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.distance">
                    <span>H</span>
                    <span class="ml10">{{ fixedNum('home_distance') + ' m' }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.horizontal_speed">
                    <span>H.S</span>
                    <span class="ml10">{{ fixedNum('horizontal_speed') + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.vertical_speed">
                    <span>V.S</span>
                    <span class="ml10">{{ fixedNum('vertical_speed') + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.wind_speed">
                    <span>W.S</span>
                    <span class="ml10">{{ fixedNum('wind_speed', 10) + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>
  
        </div>
        <!-- 机场OSD -->
        <div v-if="osdVisible.visible && osdVisible.is_dock" v-drag-window class="osd-panel fz12">
          <div class="drag-title fz16 pl5 pr5 flex-align-center flex-row flex-justify-between"
            style="border-bottom: 1px solid #515151; height: 10%;">
            <span>{{ osdVisible.gateway_callsign }}</span>
          </div>
          <span><a style="color: white; position: absolute; top: 5px; right: 5px;"
              @click="() => osdVisible.visible = false">
              <CloseOutlined />
            </a></span>
          <!-- 机场 -->
          <div class="flex-display" style="border-bottom: 1px solid #515151;">
            <div class="flex-column flex-align-center flex-justify-center" style="width: 60px; background: #2d2d2d;">
              <a-tooltip :title="osdVisible.device_name">
                <div class="flex-column  flex-align-center flex-justify-center" style="width: 90%;">
                  <span>
                    <RobotFilled style="font-size: 48px;" />
                  </span>
                  <span style="text-align: center;" class="mt10">{{ osdVisible.device_name }}</span>
                </div>
              </a-tooltip>
            </div>
            <div class="osd flex-1" style="flex: 1">
              <a-row>
                <a-col span="16"
                  :style="deviceInfo.dock.basic_osd?.mode_code === EDockModeCode.未连接 ? 'color: red; font-weight: 700;' : 'color: rgb(25,190,107)'">
                  {{ EDockModeCode[deviceInfo.dock.basic_osd?.mode_code] }}</a-col>
              </a-row>
              <!-- <a-row>
                <a-col span="12">
                  <a-tooltip :title="dockTitle.acc_time">
                    <span>
                      <HistoryOutlined />
                    </span>
                    <span class="ml10">
                      <span v-if="deviceInfo.dock.work_osd?.acc_time >= 2592000"> {{
                        Math.floor(deviceInfo.dock.work_osd?.acc_time / 2592000) }}m </span>
                      <span v-if="(deviceInfo.dock.work_osd?.acc_time % 2592000) >= 86400"> {{
                        Math.floor((deviceInfo.dock.work_osd?.acc_time % 2592000) / 86400) }}d </span>
                      <span v-if="(deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400) >= 3600"> {{
                        Math.floor((deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400) / 3600) }}h </span>
                      <span v-if="(deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400 % 3600) >= 60"> {{
                        Math.floor((deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400 % 3600) / 60) }}min </span>
                      <span>{{ Math.floor(deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400 % 3600 % 60) }} s</span>
                    </span>
                  </a-tooltip>
                </a-col>
                <a-col span="12">
                  <a-tooltip :title="dockTitle.activation_time">
                    <span>
                      <FieldTimeOutlined />
                    </span>
                    <span class="ml10">{{ new Date((deviceInfo.dock.work_osd?.activation_time ?? 0) *
                      1000).toLocaleString() }}
                    </span>
                  </a-tooltip>
                </a-col>
              </a-row> -->
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.network_state">
                    <span :style="qualityStyle">
                      <span v-if="deviceInfo.dock.basic_osd?.network_state?.type === NetworkStateTypeEnum.FOUR_G">
                        <SignalFilled />
                      </span>
                      <span v-else>
                        <GlobalOutlined />
                      </span>
                    </span>
                    <span class="ml10">{{ deviceInfo.dock.basic_osd?.network_state?.rate }} kb/s</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.job_number">
                    <span>
                      <CarryOutOutlined />
                    </span>
                    <span class="ml10">{{ deviceInfo.dock.work_osd?.job_number }} </span>
                  </a-tooltip>
                </a-col>
                <a-col span="6" :title="dockTitle.remain_upload">
                  <a-tooltip title="媒体文件上传">
                    <span>
                      <CloudUploadOutlined class="fz14" />
                    </span>
                    <span class="ml10">{{ deviceInfo.dock.link_osd?.media_file_detail?.remain_upload }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip>
                    <template #title>
                      <p>{{ dockTitle.total }}: {{ deviceInfo.dock.basic_osd?.storage?.total }}</p>
                      <p>{{ dockTitle.used }}: {{ deviceInfo.dock.basic_osd?.storage?.used }}</p>
                    </template>
                    <span>
                      <FolderOpenOutlined />
                    </span>
                    <span class="ml10" v-if="deviceInfo.dock.basic_osd?.storage?.total > 0">
                      <el-progress type="circle" :width="20"
                        :percentage="deviceInfo.dock.basic_osd?.storage?.used * 100 / deviceInfo.dock.basic_osd?.storage?.total"
                        :stroke-width="2" :show-text="showprogress"
                        :color="deviceInfo.dock.basic_osd?.storage?.used * 100 / deviceInfo.dock.basic_osd?.storage?.total > 80 ? 'red' : '#00ee8b'" />
                    </span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.wind_speed">
                    <span>W.S</span>
                    <span class="ml10">{{ (deviceInfo.dock.basic_osd?.wind_speed ?? str) + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.rainfall">
                    <span>🌧</span>
                    <span class="ml10">{{ RainfallEnum[deviceInfo.dock.basic_osd.rainfall] }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.environment_temperature">
                    <span>°C</span>
                    <span class="ml10">{{ deviceInfo.dock.basic_osd?.environment_temperature }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.dock_temperature">
                    <span>°C</span>
                    <span class="ml10">{{ deviceInfo.dock.basic_osd?.temperature }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.dock_humidity">
                    <span>💦</span>
                    <span class="ml10">{{ deviceInfo.dock.basic_osd?.humidity }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.working_voltage + '，正常电压范围：25500mV~28500mV'">
                    <span
                      style="border: 1px solid; border-radius: 50%; width: 18px; height: 18px; line-height: 16px; text-align: center; float: left;">V</span>
                    <span class="ml10">{{ (deviceInfo.dock.work_osd?.working_voltage ?? str) + ' mV' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.working_current">
                    <span
                      style="border: 1px solid; border-radius: 50%; width: 18px; height: 18px; line-height: 15px; text-align: center; float: left;">A</span>
                    <span class="ml10">{{ (deviceInfo.dock.work_osd?.working_current ?? str) + ' mA' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="dockTitle.drone_in_dock">
                    <span>
                      <RocketOutlined />
                    </span>
                    <span class="ml10">{{ deviceInfo.dock.basic_osd?.drone_in_dock }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row class="p5">
                <a-col span="24">
                  <a-button type="primary" :disabled="dockControlPanelVisible" size="small"
                    @click="setDockControlPanelVisible(true)" v-permission>
                    操作
                  </a-button>
                </a-col>
              </a-row>
              <!-- 机场控制面板 -->
              <DockControlPanel v-if="dockControlPanelVisible" :sn="osdVisible.gateway_sn" :deviceInfo="deviceInfo"
                @close-control-panel="onCloseControlPanel">
              </DockControlPanel>
            </div>
          </div>
          <!--  飞机-->
          <div class="flex-display" style="border-bottom: 2px solid #515151;">
            <div class="flex-column flex-align-center flex-justify-center" style="width: 60px;  background: #2d2d2d;">
              <a-tooltip :title="osdVisible.model">
                <div style="width: 90%;" class="flex-column flex-align-center flex-justify-center">
                  <span><a-image :src="M30" :preview="false" /></span>
                  <span>{{ osdVisible.model }}</span>
                </div>
              </a-tooltip>
            </div>
            <div class="osd flex-1">
              <a-row>
                <a-col span="16"
                  :style="!deviceInfo.device || deviceInfo.device?.mode_code === EModeCode.未连接 ? 'color: red; font-weight: 700;' : 'color: rgb(25,190,107)'">
                  {{ !deviceInfo.device ? EModeCode[EModeCode.未连接] : EModeCode[deviceInfo.device?.mode_code]
                  }}</a-col>
              </a-row>
              <a-row>
                <!-- <a-col span="6">
                  <a-tooltip :title="deviceTitle.upward_quality">
                    <span>
                      <SignalFilled />
                      <ArrowUpOutlined style="font-size: 9px; vertical-align: top;" />
                    </span>
                    <span class="ml10">{{ deviceInfo.dock.link_osd?.sdr?.up_quality }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.downward_quality">
                    <span>
                      <SignalFilled />
                      <ArrowDownOutlined style="font-size: 9px; vertical-align: top;" />
                    </span>
                    <span class="ml10">{{ deviceInfo.dock.link_osd?.sdr?.down_quality }}</span>
                  </a-tooltip>
                </a-col> -->
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.capacity_percent">
                    <span>
                      <ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14" />
                    </span>
                    <span class="ml10">{{ deviceInfo.device && deviceInfo.device.battery.capacity_percent !== str ?
                      deviceInfo.device?.battery.capacity_percent + ' %' : str }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip>
                    <template #title>
                      <p>{{ deviceTitle.total }}: {{ deviceInfo.device?.storage?.total }}</p>
                      <p>{{ deviceTitle.used }}: {{ deviceInfo.device?.storage?.used }}</p>
                    </template>
                    <span>
                      <FolderOpenOutlined />
                    </span>
                    <span class="ml10" v-if="deviceInfo.device?.storage?.total > 0">
                      <el-progress type="circle" :width="20"
                        :percentage="deviceInfo.device?.storage?.used * 100 / deviceInfo.device?.storage?.total"
                        :strokeWidth="2" :stroke-width="2" :show-text="showprogress"
                        :strokeColor="deviceInfo.device?.storage?.used * 100 / deviceInfo.device?.storage?.total > 80 ? 'red' : '#00ee8b'" />
                    </span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-tooltip :title="deviceTitle.rtk_fixed">
                  <a-col span="6" class="flex-row flex-align-center flex-justify-start">
                    <span>Fixed</span>
                    <span class="ml10 circle"
                      :style="deviceInfo?.device?.position_state.is_fixed === 1 ? 'backgroud: rgb(25,190,107);' : ' background: red;'"></span>
                  </a-col>
                </a-tooltip>
                <a-col span="6">
                  <a-tooltip title="GPS">
                    <span>GPS</span>
                    <span class="ml10">{{ deviceInfo.device ? deviceInfo.device.position_state.gps_number : str }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.rtk">
                    <span>
                      <TrademarkOutlined class="fz14" />
                    </span>
                    <span class="ml10">{{ deviceInfo.device ? deviceInfo.device.position_state.rtk_number : str }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.gear">
                    <span>
                      <ControlOutlined class="fz16" />
                    </span>
                    <span class="ml10">{{ deviceInfo.device ? EGear[deviceInfo.device?.gear] : str }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.Altitude">
                    <span>ASL</span>
                    <span class="ml10">{{ fixedNum('height') + ' m' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.takeoff">
                    <span>ALT</span>
                    <span class="ml10">{{ fixedNum('elevation') + ' m' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.distance">
                    <span
                      style="border: 1px solid; border-radius: 50%; width: 16px; height: 16px; line-height: 15px; text-align: center;  display: block; float: left;">H</span>
                    <span class="ml10">{{ fixedNum('home_distance') + ' m' }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.horizontal_speed">
                    <span>H.S</span>
                    <span class="ml10">{{ fixedNum('horizontal_speed') + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.vertical_speed">
                    <span>V.S</span>
                    <span class="ml10">{{ fixedNum('vertical_speed') + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.wind_speed">
                    <span>W.S</span>
                    <span class="ml10">{{ fixedNum('wind_speed', 10) + ' m/s' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.camera_mode">
                    <span>
                      <CameraOutlined class="fz16" />
                    </span>
                    <span class="ml10">
                      {{
                        deviceInfo.device && deviceInfo.device.cameras ? (deviceInfo.device.cameras[0].camera_mode == 0 ?
                          '拍照' :
                          deviceInfo.device.cameras[0].camera_mode == 1 ? '录像' : deviceInfo.device.cameras[0].camera_mode == 2
                            ?
                            '智能低光' : '全景拍照') : str
                      }}
                    </span>
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>
          <div class="battery-slide" v-if="deviceInfo.device && deviceInfo.device.battery.remain_flight_time !== 0"
            style="border: 1px solid red">
            <div style="background: #535759;" class="width-100"></div>
            <div class="capacity-percent" :style="{ width: deviceInfo.device.battery.capacity_percent + '%' }"></div>
            <div class="return-home" :style="{ width: deviceInfo.device.battery.return_home_power + '%' }"></div>
            <div class="landing" :style="{ width: deviceInfo.device.battery.landing_power + '%' }"></div>
            <div class="white-point" :style="{ left: deviceInfo.device.battery.landing_power + '%' }"></div>
            <div class="battery" :style="{ left: deviceInfo.device.battery.capacity_percent + '%' }">
              {{ Math.floor(deviceInfo.device.battery.remain_flight_time / 60) }}:
              {{ 10 > (deviceInfo.device.battery.remain_flight_time % 60) ? '0' :
                '' }}{{ deviceInfo.device.battery.remain_flight_time % 60 }}
            </div>
          </div>
          <!-- 飞行指令 -->
          <!--        <DroneControlPanel :sn="osdVisible.gateway_sn" :deviceInfo="deviceInfo" :payloads="osdVisible.payloads">-->
          <!--        </DroneControlPanel>-->
        </div>
        <!-- liveview -->
        <div class="liveview" v-if="livestreamOthersVisible" v-drag-window>
          <div style="height: 40px; width: 100%" class="drag-title"></div>
          <a style="position: absolute; right: 10px; top: 10px; font-size: 16px; color: white;"
            @click="closeLivestreamOthers">
            <CloseOutlined />
          </a>
          <LivestreamOthers />
        </div>
  
      </div>
  
      <!-- 添加坐标信息底部栏 -->
      <div class="map-coordinates-bar">
        <div class="coordinates-info">
          <span>经度: {{ mousePosition.longitude.toFixed(6) }}</span>
          <span>纬度: {{ mousePosition.latitude.toFixed(6) }}</span>
          <a-tooltip placement="top">
            <template #title>
              <div>
                <p><strong>海拔高度 (Above Sea Level)</strong></p>
                <p>指相对于平均海平面的高度，常用于飞行导航和地形分析。</p>
                <p>在无人机飞行中，ASL决定了飞行高度限制和障碍物安全裕度。</p>
              </div>
            </template>
            <span>ASL: {{ mousePosition.asl.toFixed(2) }} m</span>
          </a-tooltip>
          <a-tooltip placement="top">
            <template #title>
              <div>
                <p><strong>椭球高度 (Height Above Ellipsoid)</strong></p>
                <p>指相对于WGS84参考椭球体的高度，是GPS原始测量值。</p>
                <p>与ASL的区别：HAE是纯几何测量值，而ASL考虑了地球重力场变化。</p>
                <p>在某些地区，HAE与ASL可能有几十米的差异。</p>
              </div>
            </template>
            <span>HAE: {{ mousePosition.hae.toFixed(2) }} m</span>
          </a-tooltip>
        </div>
      </div>
    </div>
  </template>
  
  <script lang="ts">
  import { defineComponent, onBeforeMount, onMounted, onBeforeUnmount, ref, reactive, watch, computed } from 'vue';
  import { generateLineContent, generatePointContent, generatePolyContent } from '@/utils/map-layer-utils';
  import store from '@/store';
  import { onBeforeRouteUpdate, useRoute } from 'vue-router';
  import { MapDoodleType, MapElementEnum } from '@/constants/map';
  import { getApp, getRoot } from '@/root';
  import { postElementsReq, getElementGroupsReq } from '@/api/layer';
  import { useGMapManage } from '@/hooks/use-c-map';
  import { useMouseTool } from '@/hooks/use-mouse-tool';
  import { useFlightArea } from '../flight-area/use-flight-area';
  import { EFlightAreaType, EGeometryType } from '../../types/flight-area';
  import { MapDoodleEnum } from '@/types/map-enum';
  import { uuidv4 } from '@/utils/uuid';
  import { PostElementsBody } from '@/types/mapLayer';
  import EventBus from '@/event-bus';
  import { useConnectMqtt } from '../g-map/use-connect-mqtt';
  import { EDeviceTypeName } from '../../types';
  import { GeojsonCoordinate, LayerResource } from '@/types/map';
  import { useGMapCover } from '@/hooks/use-c-map-cover';
  import { deviceTsaUpdate } from '@/hooks/use-c-map-tsa';
  import { useFlightAreaDroneLocationEvent } from '../flight-area/use-flight-area-drone-location-event';
  import { BorderOutlined, LineOutlined, CloseOutlined, AimOutlined, EyeOutlined, EyeInvisibleOutlined, GlobalOutlined, CheckOutlined } from '@ant-design/icons-vue';
  import FlightAreaActionIcon from '../flight-area/FlightAreaActionIcon.vue';
  import pin from '@/assets/pin-2d8cf0.svg';
  import M30 from '@/assets/m30.png';
  import DockControlPanel from '../g-map/DockControlPanel.vue';
  import { useDockControl } from '../g-map/use-dock-control';
  import DroneControlPanel from '../g-map/DroneControlPanel.vue';
  import {
    ControlOutlined, TrademarkOutlined, ArrowDownOutlined, CameraOutlined,
    ThunderboltOutlined, SignalFilled, HistoryOutlined, CloudUploadOutlined, RocketOutlined,
    FieldTimeOutlined, CloudOutlined, CloudFilled, FolderOpenOutlined, RobotFilled, ArrowUpOutlined, CarryOutOutlined
  } from '@ant-design/icons-vue';
  import { EGear, EModeCode, EDockModeCode, } from '@/types/device';
  import { deviceTitle, dockTitle, NetworkStateQualityEnum, NetworkStateTypeEnum, RainfallEnum, DrcStateEnum, FourGLinkStateEnum, SdrLinkStateEnum, LinkWorkModeEnum, DroneBatteryStateEnum } from '@/api/enum/index';
  import { GetFlightArea, getFlightAreaList } from '@/api/flight-area';
  import { useMapTool } from '@/hooks/use-map-tool';
  import { message } from 'ant-design-vue';
  import LivestreamOthers from '../livestream-others.vue';
  import { mapGetters } from 'vuex';
  import { testDroneTrackDisplay } from '@/hooks/use-c-map-tsa';
  import { useMousePosition } from '@/hooks/use-mouse-position';
  
  export default defineComponent({
    components: {
      LivestreamOthers, DroneControlPanel, DockControlPanel, FlightAreaActionIcon,
      BorderOutlined, LineOutlined, CloseOutlined, ControlOutlined, TrademarkOutlined,
      ArrowDownOutlined, ThunderboltOutlined, SignalFilled, GlobalOutlined, CarryOutOutlined,
      HistoryOutlined, CloudUploadOutlined, RocketOutlined, CameraOutlined, ArrowUpOutlined,
      FieldTimeOutlined, CloudOutlined, CloudFilled, FolderOpenOutlined, RobotFilled, AimOutlined,
      EyeOutlined, EyeInvisibleOutlined, CheckOutlined
    },
    computed: {
      ...mapGetters(['mapType', 'layerMapType']),
    },
    setup() {
      onBeforeUnmount(() => {
        deviceTsaUpdateHook.removeAllMarker();
      });
  
      onBeforeRouteUpdate(() => {
        osdVisible.value.visible = false;
      });
  
      const osdVisible = computed(() => {
        return store.state.dock.osdVisible
      })
      let useGMapCoverHook = useGMapCover();
      let useMapToolHook = useMapTool();
      const str = '--'
  
      const livestreamOthersVisible = computed(() => {
        return store.state.dock.livestreamOthersVisible
      })
      const livestreamAgoraVisible = computed(() => {
        return store.state.dock.livestreamAgoraVisible
      })
  
      const deviceInfo = reactive({
        gateway: {
          capacity_percent: str,
          transmission_signal_quality: str,
        },
        dock: {
          basic_osd: {
            network_state: {
              type: null, // 示例值
              quality: null, // 示例值
              rate: null, // 示例值（可能是比特率）
            },
            drone_charge_state: {
              state: 1, // 示例值，可能代表充电中
              capacity_percent: 90,
            },
            drone_in_dock: true,
            rainfall: 0,
            wind_speed: 5.0, // 米/秒
            environment_temperature: 22.5,
            temperature: 25.0, // 设备温度
            humidity: 60,
            latitude: 34.0522,
            longitude: -118.2437,
            height: 10.0, // 米
            alternate_land_point: {
              latitude: 34.0530,
              longitude: -118.2440,
              height: 5.0,
              safe_land_height: 3.0,
              is_configured: 1, // 假设1表示已配置
            },
            first_power_on: 1609459200000, // 示例时间戳（2021年1月1日）
            positionState: {
              gps_number: 4, // 示例值
              is_fixed: 1, // 假设1表示已固定
              rtk_number: 0, // 示例值
              is_calibration: 0, // 假设0表示未校准
              quality: 3, // 示例值
            },
            storage: {
              total: 1024, // 示例总容量
              used: 512, // 示例已用量
            },
            mode_code: 1, // 示例模式代码
            cover_state: 1, // 示例覆盖状态
            supplement_light_state: 0, // 示例补光灯状态
            emergency_stop_state: 0, // 示例紧急停止状态
            air_conditioner: {
              air_conditioner_state: 0, // 示例空调状态
              switch_time: 1609459200000, // 示例切换时间（与first_power_on相同，仅为示例）
            },
            battery_store_mode: 0, // 示例电池保养模式
            alarm_state: 0, // 示例报警状态
            putter_state: 1, // 示例放置机构状态
            sub_device: {
              device_sn: '',
              device_model_key: '',
              device_online_status: 1, // 假设1表示在线
              device_paired: 1, // 假设1表示已配对
            },
          },
          link_osd: {
            drc_state: DrcStateEnum.DISCONNECT, // 示例值  
            flighttask_prepare_capacity: 100, // 示例值  
            flighttask_step_code: 1, // 示例值  
            media_file_detail: {
              remain_upload: 50, // 示例值  
            },
            sdr: {
              up_quality: 'HD', // 示例值  
              down_quality: 'HD', // 示例值  
              frequency_band: 2.4, // 示例值  
            },
            wireless_link: {
              dongle_number: 2, // dongle 数量  
              '4g_link_state': FourGLinkStateEnum.OPEN, // 4G链路状态  
              sdr_link_state: SdrLinkStateEnum.CLOSE, // SDR链路连接状态  
              link_workmode: LinkWorkModeEnum.SDR, // 图传链路模式  
              sdr_quality: 4, // SDR信号质量 0-5  
              '4g_quality': 3, // 4G信号质量 0-5  
              '4g_freq_band': 800, // 4G频率带  
              '4g_gnd_quality': 3, // 4G地面信号质量  
              '4g_uav_quality': 4, // 4G无人机信号质量  
              sdr_freq_band: 2.4, // SDR频率带  
            },
          },
          work_osd: {
            job_number: 0,
            acc_time: null,
            activation_time: 0,
            maintain_status: {
              maintain_status_array: []
            },
            electric_supply_voltage: null,
            working_voltage: null,
            working_current: null,
            backup_battery: {
              voltage: null,
              temperature: null,
              switch: null,
            },
            drone_battery_maintenance_info: { // 飞行器电池保养信息
              maintenance_state: DroneBatteryStateEnum.MaintenanceInProgress, // 保养状态
              maintenance_time_left: 0, // 电池保养剩余时间(小时)
            }
          }
        },
        device: {
          gear: -1,
          mode_code: EModeCode.未连接,
          height: str,
          home_distance: str,
          horizontal_speed: str,
          vertical_speed: str,
          wind_speed: 0,
          wind_direction: str,
          elevation: str,
          position_state: {
            gps_number: str,
            is_fixed: 0,
            rtk_number: str
          },
          battery: {
            capacity_percent: str,
            landing_power: str,
            remain_flight_time: 0,
            return_home_power: str,
          },
          latitude: 0,
          longitude: 0,
        }
      });
  
      const state = reactive({
        currentType: '',
        coverIndex: 0,
        isFlightArea: false,
        polylineNum: 0,
        PolygonNum: 0,
        sceneMode: '3D', // 添加视图模式，默认为3D
        flightAreaType: '', // 添加飞行区域类型
      });
  
      const qualityStyle = computed(() => {
        if (deviceInfo.dock.basic_osd?.network_state?.type === NetworkStateTypeEnum.ETHERNET ||
          (deviceInfo.dock.basic_osd?.network_state?.quality || 0) > NetworkStateQualityEnum.FAIR) {
          return 'color: #00ee8b'
        }
        if ((deviceInfo.dock.basic_osd?.network_state?.quality || 0) === NetworkStateQualityEnum.FAIR) {
          return 'color: yellow'
        }
        return 'color: red'
      })
      const showprogress = ref(false)
  
      watch(() => store.state.dock.deviceStatusEvent.deviceOffline,
        data => {
          if (data && data.length !== 0 && data.sn) {
            deviceTsaUpdateHook.removeMarker(data.sn)
            if (osdVisible.value.is_dock && data.sn === osdVisible.value.gateway_sn) {
              store.commit('SET_OSD_VISIBLE_ClOSE')
            }
            store.commit('setCockpitUrl', '');
          }
        },
        {
          deep: true
        }
      );
      watch(() => store.state.dock.deviceState,
        data => {
          if (store.state.common.jscShow) {
            return;
          }
          //遥控器
          if (data.currentType === EDeviceTypeName.Gateway && data.gatewayInfo[data.currentSn]) {
            const { longitude, latitude, height } = data.gatewayInfo[data.currentSn]
            if (longitude && latitude) {
              deviceTsaUpdateHook.moveTo(data.currentSn, longitude, latitude, height)
            }
            if (osdVisible.value.visible && osdVisible.value.gateway_sn !== '') {
              deviceInfo.gateway = data.gatewayInfo[osdVisible.value.gateway_sn]
            }
          }
          //无人机
          if (data.currentType === EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
            const { longitude, latitude, height } = data.deviceInfo[data.currentSn]
            if (longitude && latitude) {
              console.log('longitude', longitude, 'latitude', latitude, 'height', height)
              deviceTsaUpdateHook.moveTo(data.currentSn, longitude, latitude, height)
            }
            if (osdVisible.value.visible && osdVisible.value.sn !== '') {
              deviceInfo.device = data.deviceInfo[osdVisible.value.sn]
            }
          }
          //机场数据
          if (data.currentType === EDeviceTypeName.Dock && data.dockInfo[data.currentSn]) {
            if (!data.dockInfo[data.currentSn]?.basic_osd) {
              return;
            }
            const { longitude, latitude } = data.dockInfo[data.currentSn]?.basic_osd;
            if (longitude && latitude) {
              deviceTsaUpdateHook.initMarker(EDeviceTypeName.Dock, EDeviceTypeName[EDeviceTypeName.Dock], data.currentSn, longitude, latitude)
            }
            if (osdVisible.value.visible && osdVisible.value.is_dock) {
              deviceInfo.dock = data.dockInfo[osdVisible.value.gateway_sn]
              deviceInfo.device = data.deviceInfo[deviceInfo.dock.basic_osd.sub_device?.device_sn ?? osdVisible.value.sn]
            }
          }
        }, {
        deep: true
      }
      )
      function closeLivestreamOthers() {
        store.commit('SET_LIVESTREAM_OTHERS_VISIBLE', false)
      }
      function closeLivestreamAgora() {
        store.commit('SET_LIVESTREAM_AGORA_VISIBLE', false)
      }
      function closeOSD() {
        osdVisible.value.visible = false
      }
      const mapLayers = ref(store.state.dock.Layers);
  
      const useGMapManageHook = useGMapManage();
      const useMouseToolHook = useMouseTool();
      const deviceTsaUpdateHook = deviceTsaUpdate();
      const shareId = computed(() => {
        return store.state.dock.layerBaseInfo.share;
      });
      const drawVisible = computed(() => {
        return store.state.dock.drawVisible;
      });
      const mouseMode = ref(false);
  
      /**
       * 监听路由变化移除描绘
       */
      const route = useRoute();
      watch(() => route.path, data => {
        draw('off', false)
        console.log('data', data);
        if (data !== '/tsa/index') {
          deviceTsaUpdateHook.removeAllMarker();
          store.commit("COCKPIT_SET", true)
        } else {
          store.commit("COCKPIT_SET", false)
        }
      }, {
        immediate: true
      })
  
      const flightAreaList = ref<GetFlightArea[]>([]);
      //获取自定义飞行数据
      const getDataList = () => {
        getFlightAreaList()
          .then(res => {
            if (res.code === 0) {
              flightAreaList.value = res.data;
            }
          }).finally(() => {
  
          });
      };
  
      function getAllElement() {
        getElementGroups('init');
        setTimeout(() => {
          useGMapCoverHook = useGMapCover();
        }, 2000);
      }
  
      async function getElementGroups(type?: string) {
        const result = await getElementGroupsReq({
          groupId: '',
          isDistributed: true,
        });
        mapLayers.value = result.data;
        mapLayers.value = updateWgs84togcj02();
        if (type && type === 'init') {
          store.dispatch('setLayerInfo', mapLayers.value);
        }
        store.commit('SET_LAYER_INFO', mapLayers.value);
      }
      function updateWgs84togcj02() {
        return mapLayers.value;
      }
  
      watch(
        () => store.state.dock.Layers,
        newData => {
          store.commit('SET_LAYER_INFO', newData);
        },
        {
          deep: true,
        }
      );
  
      function draw(type: MapDoodleType, bool: boolean, flightAreaType?: EFlightAreaType) {
        state.currentType = type;
        mouseMode.value = bool;
        state.isFlightArea = !!flightAreaType;
        state.flightAreaType = flightAreaType || '';
        useMouseToolHook.mouseTool(type, getDrawCallback, flightAreaType);
      }
  
      const { getDrawFlightAreaCallback, onFlightAreaDroneLocationWs } = useFlightArea();
      useFlightAreaDroneLocationEvent(onFlightAreaDroneLocationWs);
      const {
        dockControlPanelVisible,
        setDockControlPanelVisible,
        onCloseControlPanel,
      } = useDockControl()
      // 连接或断开drc
      useConnectMqtt();
  
      const { mousePosition, initMousePositionListener } = useMousePosition();
  
      onMounted(() => {
        const app = getApp();
        useGMapManage().globalPropertiesConfig(app);
        // 测试无人机轨迹显示
        // setTimeout(() => {
        //   testDroneTrackDisplay();
        // }, 2000);
  
        // 初始化鼠标位置监听
        setTimeout(() => {
          initMousePositionListener();
        }, 1000);
      });
      function selectFlightAreaAction({ type, isCircle }: { type: EFlightAreaType, isCircle: boolean }) {
        draw(isCircle ? MapDoodleEnum.CIRCLE : MapDoodleEnum.POLYGON, true, type)
      }
      function getDrawCallback({ obj }: { obj: any }) {
        if (state.isFlightArea) {
          getDrawFlightAreaCallback(obj, state.flightAreaType);
          return;
        }
        switch (state.currentType) {
          case MapDoodleEnum.PIN:
            postPinPositionResource(obj);
            break;
          case MapDoodleEnum.POLYLINE:
            postPolylineResource(obj);
            break;
          case MapDoodleEnum.POLYGON:
            postPolygonResource(obj);
            break;
          default:
            break;
        }
      }
      async function postPinPositionResource(obj) {
        const req = getPinPositionResource(obj);
        setLayers(req);
        const coordinates = req.resource.content.geometry.coordinates;
  
        (req.resource.content.geometry.coordinates as GeojsonCoordinate).push(
          (coordinates as GeojsonCoordinate)[2]
        );
        const result = await postElementsReq(shareId.value, req);
        // obj.setExtData({ id: req.id, name: req.name });
        store.state.dock.coverMap[req.id] = [obj];
        getElementGroups('init');
      }
      /**
       * 描绘线
       * @param obj 
       */
      async function postPolylineResource(obj) {
        const req = getPolylineResource(obj);
        if (req.resource.content.geometry.coordinates.length < 2) {
          message.warning('请至少绘制两个点');
          return
        }
        const result = await postElementsReq(shareId.value, req);
        if (result.code == 0) {
          setLayers(req);
          // obj.setExtData({ id: req.id, name: req.name });
          store.state.dock.coverMap[req.id] = [obj];
          getElementGroups('init');
        }
      }
      /**
       * 描绘面
       */
      async function postPolygonResource(obj) {
        const req = getPoygonResource(obj);
        const selectItem: any = req.resource.content.geometry.coordinates[0]
        if (selectItem.length < 3) {
          message.warning('请至少绘制三个点');
          return
        }
        setLayers(req);
        const result = await postElementsReq(shareId.value, req);
        // obj.setExtData({ id: req.id, name: req.name });
        store.state.dock.coverMap[req.id] = [obj];
        getElementGroups('init');
      }
      function getPinPositionResource(obj) {
        const position = obj.getPosition();
        const resource = generatePointContent(position);
        const name = obj._originOpts.title;
        const id = uuidv4();
        return {
          id,
          name,
          resource,
        };
      }
      function getPolylineResource(obj) {
        // 从Cesium实体中获取坐标信息
        const positions = obj.polyline.positions._value;
        const positionsArray = [];
  
        // 转换Cartesian3坐标为经纬度
        for (let i = 0; i < positions.length; i++) {
          const cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
          const lng = Cesium.Math.toDegrees(cartographic.longitude);
          const lat = Cesium.Math.toDegrees(cartographic.latitude);
          positionsArray.push({
            lng: lng,
            lat: lat
          });
        }
  
        const resource = generateLineContent(positionsArray);
        const name = obj.name || `polyline${state.polylineNum++}`;
        const id = uuidv4();
        return {
          id,
          name,
          resource,
        };
      }
  
      function getPoygonResource(obj) {
        // 从Cesium实体中获取坐标信息
        const positions = obj.polygon.hierarchy._value.positions;
        const positionsArray = [];
  
        // 转换Cartesian3坐标为经纬度
        for (let i = 0; i < positions.length; i++) {
          const cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
          const lng = Cesium.Math.toDegrees(cartographic.longitude);
          const lat = Cesium.Math.toDegrees(cartographic.latitude);
          positionsArray.push({
            lng: lng,
            lat: lat
          });
        }
  
        const resource = generatePolyContent(positionsArray);
        const name = obj.name || `polygon${state.PolygonNum++}`;
        const id = uuidv4();
        return {
          id,
          name,
          resource,
        };
      }
      function getBaseInfo(obj) {
        const name = obj.title;
        const id = uuidv4();
        return { name, id };
      }
      function setLayers(resource: PostElementsBody) {
        const layers = store.state.dock.Layers;
        const layer = layers.find(item => item.id.includes(shareId.value));
        if (layer?.elements) {
          (layer?.elements as any[]).push(resource);
        }
        console.log('layers', layers);
        store.commit('SET_LAYER_INFO', layers);
      }
  
      function updateCoordinates(transformType: string, element: any) {
        // 不进行坐标转换，直接返回
        return;
      }
      const eyeShow = ref(true);
      //隐藏图形
      function hideShow(type) {
        eyeShow.value = !eyeShow.value;
        useGMapCoverHook.hideAllCovers(type)
      }
      const fixedNum = (field, per?: number) => {
        if (!deviceInfo?.device) {
          return "--"
        }
        let val = deviceInfo?.device[field];
        if (!val || val === '--') {
          return 0.0;
        }
        val = Number(val)
        val = per ? (val / per) : val;
        return val.toFixed(2);
      }
  
      const cameraFollow = computed(() => {
        return store.state.dock.cameraFollow
      })
  
      const droneListVisible = ref(false);
      const droneList = computed(() => {
        const deviceInfo = store.state.dock.deviceState.deviceInfo || {};
        const markers = store.state.dock.markerInfo.coverMap || {};
        // 获取所有有位置信息的无人机设备
        return Object.keys(deviceInfo)
          .filter(sn => {
            // 只显示存在marker的设备
            return markers[sn];
          })
          .map(sn => {
            const device = deviceInfo[sn];
            return {
              sn,
              // 使用设备sn前6位作为名称，因为model属性可能不存在
              model: device?.model || `无人机 ${sn.substring(0, 6)}`,
              callsign: device?.callsign,
              type: EDeviceTypeName.Aircraft
            };
          });
      });
      const followingSn = ref('');
  
      // 监听cameraFollow的变化，如果关闭了跟随，清空当前跟随的设备
      watch(() => store.state.dock.cameraFollow, (newValue) => {
        if (!newValue) {
          followingSn.value = '';
        }
      });
  
      // 选择并跟随指定设备
      function selectDroneToFollow(sn) {
        if (sn === undefined) {
          followingSn.value = '';
          return;
        }
        // 如果点击当前正在跟随的设备，则切换跟随状态
        if (followingSn.value === sn && store.state.dock.cameraFollow) {
          deviceTsaUpdateHook.toggleFollow(); // 关闭跟随
          followingSn.value = '';
        } else {
          followingSn.value = sn;
          // 如果当前未开启跟随，则开启跟随
          if (!store.state.dock.cameraFollow) {
            deviceTsaUpdateHook.toggleFollow(sn);
          } else {
            // 如果已开启跟随，则切换跟随的设备
            deviceTsaUpdateHook.disableEntityFollow();
            const marker = deviceTsaUpdateHook.getMarkerBySn(sn);
            if (marker) {
              deviceTsaUpdateHook.enableEntityFollow(marker);
            }
          }
        }
  
        // 隐藏popover
        droneListVisible.value = false;
      }
  
      // 切换场景模式
      function toggleSceneMode() {
        const app = getApp();
        const viewer = app.config.globalProperties.$map;
  
        if (!viewer) return;
  
        switch (state.sceneMode) {
          case '3D':
            // 切换到2D模式
            viewer.scene.morphTo2D(1);
            state.sceneMode = '2D';
            break;
          case '2D':
            // 切换到2.5D模式
            viewer.scene.morphToColumbusView(1);
            state.sceneMode = '2.5D';
            break;
          case '2.5D':
            // 切换回3D模式
            viewer.scene.morphTo3D(1);
            state.sceneMode = '3D';
            break;
        }
      }
  
      // 获取场景模式文本
      function getSceneModeText() {
        switch (state.sceneMode) {
          case '3D':
            return '当前：3D模式 (点击切换到2D)';
          case '2D':
            return '当前：2D模式 (点击切换到2.5D)';
          case '2.5D':
            return '当前：2.5D模式 (点击切换到3D)';
          default:
            return '切换视图模式';
        }
      }
  
      return {
        selectFlightAreaAction,
        drawVisible,
        pin,
        M30,
        draw,
        mouseMode,
        deviceInfo,
        EGear,
        EModeCode,
        str,
        EDockModeCode,
        qualityStyle,
        dockControlPanelVisible,
        setDockControlPanelVisible,
        onCloseControlPanel,
        NetworkStateTypeEnum,
        NetworkStateQualityEnum,
        RainfallEnum,
        DrcStateEnum,
        FourGLinkStateEnum,
        SdrLinkStateEnum,
        LinkWorkModeEnum,
        DroneBatteryStateEnum,
        closeLivestreamOthers,
        closeLivestreamAgora,
        showprogress,
        dockTitle,
        deviceTitle,
        closeOSD,
        state,
        osdVisible,
        livestreamOthersVisible,
        livestreamAgoraVisible,
        getAllElement,
        getDataList,
        flightAreaList,
        mapLayers,
        eyeShow,
        hideShow,
        fixedNum,
        cameraFollow,
        toggleSceneMode,
        getSceneModeText,
        mousePosition,
        droneListVisible,
        droneList,
        followingSn,
        selectDroneToFollow,
      };
  
    },
  });
  </script>
  <style scoped>
  #g-container {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  :deep(.cesium-viewer-bottom) {
    display: none;
  }
  
  /* 添加底部栏样式 */
  .map-coordinates-bar {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 20px;
    z-index: 100;
  }
  
  .coordinates-info {
    display: flex;
    gap: 20px;
    font-size: 14px;
  }
  
  /* 无人机列表样式 */
  .drone-list-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .close-icon {
    cursor: pointer;
    color: #999;
  }
  
  .close-icon:hover {
    color: #666;
  }
  
  .drone-list {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .empty-list {
    padding: 10px;
    text-align: center;
    color: #999;
  }
  
  .drone-item {
    padding: 8px 10px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .drone-item:last-child {
    border-bottom: none;
  }
  
  .drone-item:hover {
    background-color: #f5f5f5;
  }
  
  .drone-item.active {
    background-color: #e6f7ff;
  }
  
  .drone-info {
    flex: 1;
  }
  
  .drone-name {
    font-weight: bold;
    margin-bottom: 4px;
  }
  
  .drone-sn {
    font-size: 12px;
    color: #999;
  }
  
  .follow-icon {
    color: #0aed8b;
  }
  </style>
  