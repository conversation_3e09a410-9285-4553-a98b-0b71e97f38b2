import { ref, watch, type Ref } from 'vue';

export function useWaveformAnimation(
  isPlaying: Ref<boolean>,
  canvasRef: Ref<HTMLCanvasElement | null>,
  getData: () => Uint8Array,
  drawFn: (ctx: CanvasRenderingContext2D, w: number, h: number, dataArray: Uint8Array) => void
) {
  const animationFrameId = ref<number | null>(null);

  const animate = () => {
    if (!isPlaying.value) {
      if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value);
        animationFrameId.value = null;
      }
      return;
    }

    animationFrameId.value = requestAnimationFrame(animate);

    const canvas = canvasRef.value;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const dataArray = getData();
    
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    drawFn(ctx, canvas.width, canvas.height, dataArray);
  };

  watch(isPlaying, (newVal) => {
    if (newVal) {
      animate();
    } else {
      if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value);
        animationFrameId.value = null;
      }
    }
  });
} 