export interface IAirsenseWarning {
  altitude: number;
  altitude_type: number;
  distance: number;
  heading: number;
  icao: string;
  latitude: number;
  longitude: number;
  relative_altitude: number;
  vert_trend: number;
  warning_level: number;
}

export interface IAirsenseWarningData {
  bid: string;
  data: IAirsenseWarning[];
  need_reply: number;
  tid: string;
  timestamp: number;
  method: 'airsense_warning';
}

export interface IAirsenseWarningPayload {
  version: string;
  timestamp: number;
  biz_code: 'airsense_warning';
  data: IAirsenseWarningData;
} 