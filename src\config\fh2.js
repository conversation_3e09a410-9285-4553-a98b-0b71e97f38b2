// 示例中的 ip 地址 *********** 替换成实际用户服务地址
const serverIP = '**************';

export const fh2Config = {
    serverUrl: `http://${serverIP}:30812`,
    wssUrl: `ws://${serverIP}:30812/duplex/web`,
    hostUrl: `http://${serverIP}:8001`,
    prjId: '********-bec4-4277-8c5e-f18098c652f1',
    projectToken: 'eyJhbGciOiJIUzUxMiIsImNyaXQiOlsidHlwIiwiYWxnIiwia2lkIl0sImtpZCI6IjU3YmQyNmEwLTYyMDktNGE5My1hNjg4LWY4NzUyYmU1ZDE5MSIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************.HL3UoBDGutXeny0UHrokLF9nMpdKGCijbj2kqgTbc4zkvZYnUI0IpA_3AWKIagfg-Fsqd_NJx0Annno8fGrygg'
};
export const fh2PaasScriptUrl = `http://${serverIP}:8001/paas.js`; 
