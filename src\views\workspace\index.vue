<template>
  <div class="workspace-container">
    <div class="project-panel">
      <div class="project-header">
        <div class="header-left">
          <arrow-left-outlined class="icon-back" @click="goBack" />
          <span class="title">项目列表</span>
        </div>
      </div>
      <div class="project-filters">
        <a-dropdown :trigger="['click']" overlayClassName="custom-dropdown-menu" @visibleChange="onVisibleChange">
          <a class="ant-dropdown-link" @click.prevent>
            {{ sortFilter }} <span class="arrow-indicator">{{ isOpen ? '▲' : '▼' }}</span>
          </a>
          <template #overlay>
            <a-menu @click="handleSortChange">
              <a-menu-item v-for="option in sortOptions" :key="option.label"
                :class="{ 'active-item': sortFilter === option.label }">
                {{ option.label }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-input v-model:value="projectName" allowClear placeholder="项目名称" class="project-search-input"
          @pressEnter="handleSearch">
          <template #suffix>
            <search-outlined class="search-icon" @click="handleSearch" />
          </template>
        </a-input>
      </div>
      <div class="project-list">
        <div class="project-item" v-for="item in projectList" :key="item.id">
          <div class="item-content">
            <div class="item-header">
              <span class="status-tag">{{ statusMap[item.status] }}</span>
              <span class="project-name">{{ item.name }}</span>
            </div>
            <p class="item-desc">{{ item.introduction }}</p>
            <p class="item-date">创建时间: {{ item.createTime }}</p>
            <div class="item-user">
              <user-outlined class="user-icon" />
              <span>{{ item.createBy }} | {{ item.createUser }}</span>
            </div>
          </div>
          <div class="item-actions" @click="handleProjectClick(item)">
            <LoginOutlined />
          </div>
        </div>
      </div>
    </div>
    <div id="cockpitMap" class="map-view"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { SearchOutlined, LoginOutlined, ArrowLeftOutlined, PlusOutlined, UserOutlined } from '@ant-design/icons-vue';
import { useGMapManage } from '@/hooks/use-c-map';
import { getApp } from "@/root";
import { getUsualList } from '@/api/workspace';

const router = useRouter();

const goBack = () => {
  router.push('/');
}

const sortOptions = [
  { label: '创建时间倒序', column: 'created_at', type: 'DESC' },
  { label: '创建时间正序', column: 'created_at', type: 'ASC' },
  { label: '项目名称倒序', column: 'name', type: 'DESC' },
  { label: '项目名称正序', column: 'name', type: 'ASC' }
];

const sortFilter = ref(sortOptions[0].label);
const isOpen = ref(false);
const projectName = ref('');
const sortColumn = ref(sortOptions[0].column);
const sortType = ref(sortOptions[0].type);

const onVisibleChange = (visible) => {
  isOpen.value = visible;
}

const handleSortChange = (e) => {
  sortFilter.value = e.key;
  const selectedOption = sortOptions.find(option => option.label === e.key);
  if (selectedOption) {
    sortColumn.value = selectedOption.column;
    sortType.value = selectedOption.type;
  }
  isOpen.value = false;
  getProjectList();
}

const handleSearch = () => {
  getProjectList();
}

const handleProjectClick = (item) => {
  router.push({
    path: '/project/index'
  })
  localStorage.setItem('projectUuid', item.uuid);
}

const projectList = ref([]);

const statusMap = ref({
  '1': '进行中',
  '2': '已归档',
});

const getProjectList = () => {
  const params = {
    current: 1,
    size: 10,
    name: projectName.value,
    sortColumn: sortColumn.value,
    sortType: sortType.value,
  }
  getUsualList(params).then(res => {
    const data = res.data.data.records;
    projectList.value = data;
  })
}

onMounted(() => {
  const app = getApp();
  let mapCenter = [120.658, 27.985262726]; // 默认中心
  useGMapManage().globalPropertiesConfig(app, mapCenter, 'cockpitMap', 'Cesium');
  getProjectList();
})
</script>

<style lang="scss" scoped>
.workspace-container {
  display: flex;
  width: 100%;
  height: 100%;
  color: #e0e0e0;
}

.project-panel {
  width: 340px;
  height: 100%;
  background-color: #232323;
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  box-sizing: border-box;
  border-right: 1px solid #3c3c3c;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
  }

  .icon-back {
    font-size: 18px;
    cursor: pointer;
    color: #a0a0a0;
    margin-right: 10px;

    &:hover {
      color: #fff;
    }
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }

  .icon-plus {
    font-size: 18px;
    cursor: pointer;
    color: #a0a0a0;
  }
}

.project-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  flex-shrink: 0;

  .ant-dropdown-link {
    cursor: pointer;
    font-size: 14px;
    color: #e0e0e0;
    display: flex;
    align-items: center;

    &:hover {
      color: #fff;
    }

    .arrow-indicator {
      margin-left: 8px;
      font-size: 12px;
    }
  }

  .search-icon {
    cursor: pointer;
    font-size: 16px;
    color: #a0a0a0;

    &:hover {
      color: #fff;
    }
  }
}

.project-list {
  flex-grow: 1;
  overflow-y: auto;
  margin-right: -10px;
  padding-right: 10px;
}

.project-item {
  background-color: #232323;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
  transition: background-color 0.2s ease;
  border: 1px solid #4f4f4f;

  &:hover {
    background-color: #383838;
  }

  .item-content {
    p {
      margin: 0;
      padding: 0;
    }

    .item-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .status-tag {
        color: #67c23a;
        border: 1px solid rgba(103, 194, 58, 0.5);
        padding: 1px 5px;
        font-size: 12px;
        margin-right: 8px;
        border-radius: 2px;
        background-color: rgba(103, 194, 58, 0.1);
      }

      .project-name {
        font-size: 16px;
        font-weight: 500;
        color: #f0f0f0;
      }
    }

    .item-desc,
    .item-date,
    .item-user {
      font-size: 12px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .item-user {
      display: flex;
      align-items: center;
      margin-bottom: 0;

      .user-icon {
        margin-right: 5px;
      }
    }
  }

  .item-actions {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 50px;
    background-color: #303030;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a9a9a9;
    font-size: 20px;
    cursor: pointer;
  }
}

.map-view {
  flex-grow: 1;
  height: 100%;
  position: relative;
}

:deep(.cesium-viewer-bottom) {
  display: none;
}

:deep(.custom-dropdown-menu) {
  background-color: #2d2d2d !important;
  border-radius: 4px;
  border: 1px solid #444;
  padding: 4px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.custom-dropdown-menu .ant-dropdown-menu-item) {
  color: #a0a0a0;
  font-size: 14px;
}

:deep(.custom-dropdown-menu .ant-dropdown-menu-item:hover) {
  background-color: #383838;
  color: #fff;
}

:deep(.custom-dropdown-menu .ant-dropdown-menu-item.active-item) {
  background-color: #265d9c;
  color: #fff;
  font-weight: 500;
}

/* Scrollbar styles for project-list */
.project-list::-webkit-scrollbar {
  width: 6px;
}

.project-list::-webkit-scrollbar-track {
  background: transparent;
}

.project-list::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.project-list::-webkit-scrollbar-thumb:hover {
  background: #666;
}

:deep(.project-search-input.ant-input-affix-wrapper) {
  width: 160px;
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
  border-radius: 4px;

  .ant-input {
    background-color: transparent;
    color: #e0e0e0;

    &::placeholder {
      color: #8c8c8c;
    }
  }

  .ant-input-clear-icon {
    color: #a0a0a0;

    &:hover {
      color: #fff;
    }
  }
}
</style>