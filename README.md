
#### 项目介绍
* 1.司空私有对接PC端

#### 项目目录：
* mock mock数据目录
* api 项目接口请求地址封装文件
* config 项目的全局配置文件
* components 项目公共组件封装模块
* mixins 分发 Vue 组件中的可复用功能
* page 项目公共菜单
* router 项目公共路由配置
* static 项目静态图片目录
* styles 项目样式CSS文件目录
* utils 项目用到的公用JS或者自定义功能文件
* store 项目用到的公用状态管理
* views 项目页面视图层开发目录

### 项目安装 建议使用yarn
* 建议node版本为18 --（18.16.0）
* 建议更换yarn镜像源 -- yarn config set registry https://registry.npmmirror.com
* 进行安装依赖 -- yarn install

#### 项目运行
* yarn install
* yarn run dev/prod

#### 项目打包
* yarn run build
