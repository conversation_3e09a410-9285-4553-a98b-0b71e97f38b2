import request  from '@/axios'
import { ELocalStorageKey } from '@/types'
import { NightLightsStateEnum, DistanceLimitStatus, ObstacleAvoidance } from '@/types/device-setting'
import { getWorkspaceId } from '@/utils/storage'

const MNG_API_PREFIX = '/hztech-flight-core/manage/api/v1'
export interface IWorkspaceResponse<T> {
  code: number;
  data: T;
  message: string;
 }
export interface PutDevicePropsBody {
  night_lights_state?: NightLightsStateEnum;// 夜航灯开关
  height_limit?: number;// 限高设置
  distance_limit_status?: DistanceLimitStatus;// 限远开关
  obstacle_avoidance?: ObstacleAvoidance;// 飞行器避障开关设置
}

/**
 * 设置设备属性
 * @param params
 * @returns
 */
//  /manage/api/v1/devices/{{workspace_id}}/devices/{{device_sn}}/property
export async function putDeviceProps (deviceSn: string, body: PutDevicePropsBody): Promise<IWorkspaceResponse<{}>> {
  const resp = await request.put(`${MNG_API_PREFIX}/devices/${getWorkspaceId()}/devices/${deviceSn}/property`, body)
  return resp.data
}
