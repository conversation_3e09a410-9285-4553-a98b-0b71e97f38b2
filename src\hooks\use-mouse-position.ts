import { reactive, onBeforeUnmount, ref } from 'vue';
import { getApp } from '@/root';

export function useMousePosition() {
  // 鼠标位置状态
  const mousePosition = reactive({
    longitude: 0,
    latitude: 0,
    asl: 0, // Above Sea Level (海拔高度)
    hae: 0  // Height Above Ellipsoid (椭球面高度)
  });

  // 定义鼠标处理器引用
  const mouseHandler = ref(null);

  // 初始化鼠标位置监听
  function initMousePositionListener() {
    const app = getApp();
    const viewer = app.config.globalProperties.$map;

    if (!viewer) return;

    // 创建新的屏幕空间事件处理器
    mouseHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

    // 监听鼠标移动事件
    mouseHandler.value.setInputAction((movement) => {
      try {
        // 获取鼠标位置的射线
        const ray = viewer.camera.getPickRay(movement.endPosition);
        if (!ray) return;

        // 判断当前场景模式
        const is3DMode = viewer.scene.mode === 3;          

        // 尝试与地形相交获取精确的位置（包括高度）
        const terrainCartesian = is3DMode ? viewer.scene.globe.pick(ray, viewer.scene) : null;

        // 尝试与椭球相交获取位置
        const ellipsoidCartesian = viewer.camera.pickEllipsoid(
          movement.endPosition,
          viewer.scene.globe.ellipsoid
        );

        // 确定使用哪个位置
        const cartesian = terrainCartesian || ellipsoidCartesian;

        if (cartesian) {
          // 转换为地理坐标
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

          // 获取经纬度
          const longitude = Cesium.Math.toDegrees(cartographic.longitude);
          const latitude = Cesium.Math.toDegrees(cartographic.latitude);

          // HAE (Height Above Ellipsoid) - 椭球面高度
          // 这是点到椭球体的垂直距离
          const hae = cartographic.height;

          // ASL (Above Sea Level) - 海拔高度计算
          let asl = 0;

          // 计算ASL的更好方法
          if (terrainCartesian) {
            // 点在地形上，我们就直接使用HAE作为ASL
            // 在现实世界中，这两个值应该有差异，通常是大地水准面偏移 
            // 但是在Cesium中，地形高度已经相对于椭球体定义了
            asl = hae;
          } else {
            // 点在椭球面上或空中
            // 在这种情况下，我们可以估计ASL约等于HAE
            // 或者如果有地形数据，我们可以尝试查询该点的地形高度
            if (is3DMode && viewer.terrainProvider) {
              try {
                // 通过查询该位置的地形高度来获取更准确的值
                const terrainHeight = viewer.scene.globe.getHeight(cartographic);
                if (terrainHeight !== undefined && terrainHeight !== null) {
                  // 如果我们能获取地形高度，使用它
                  asl = terrainHeight;
                } else {
                  // 没有地形高度，使用HAE
                  asl = hae;
                }
              } catch (e) {
                // 如果查询失败，使用HAE
                asl = hae;
              }
            } else {
              // 如果没有地形或不是3D模式，使用HAE
              asl = hae;
            }
          }

          // 确保ASL不为负数（在Cesium中，负数通常表示地下，但在界面上显示负值不太直观）
          asl = Math.max(0, asl);

          // 更新UI显示
          mousePosition.longitude = longitude;
          mousePosition.latitude = latitude;
          mousePosition.hae = parseFloat(hae.toFixed(2));  // 保留两位小数
          mousePosition.asl = parseFloat(asl.toFixed(2));  // 保留两位小数
        }
      } catch (error) {
        console.error('Error tracking mouse position:', error);
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }

  // 清理函数，确保组件卸载时移除事件处理器
  function destroyMousePositionListener() {
    if (mouseHandler.value) {
      mouseHandler.value.destroy();
      mouseHandler.value = null;
    }
  }

  // 组件卸载时自动清理
  onBeforeUnmount(() => {
    destroyMousePositionListener();
  });

  return {
    mousePosition,
    initMousePositionListener,
    destroyMousePositionListener
  };
} 