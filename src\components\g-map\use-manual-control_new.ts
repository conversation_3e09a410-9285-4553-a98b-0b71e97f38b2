import {
  ref,
  onUnmounted,
  watch,
  Ref,
} from 'vue'
import { message } from 'ant-design-vue'
import {
  DRC_METHOD,
  DroneControlProtocol,
  StickControlProtocol,
} from '@/types/drc'
import {
  useMqtt,
  DeviceTopicInfo
} from './use-mqtt'

let myInterval: any

export enum KeyCode {
  KEY_W = 'KeyW',
  KEY_A = 'KeyA',
  KEY_S = 'KeyS',
  KEY_D = 'KeyD',
  KEY_Q = 'KeyQ',
  KEY_E = 'KeyE',
  ARROW_UP = 'ArrowUp',
  ARROW_DOWN = 'ArrowDown',
}

export function useManualControl (deviceTopicInfo: DeviceTopicInfo, isCurrentFlightController: Ref<boolean>, useStickControl: boolean) {
  const activeCodeKey = ref(null) as Ref<KeyCode | null>
  const mqttHooks = useMqtt(deviceTopicInfo)
  const params: DroneControlProtocol = {};
  let seq = 0;
  let zoomSetSeq =0;
  let drcIntervalPhotoSetSeq = 0;

  const STICK_MID = 1024;
  const STICK_OFFSET = 660;
  let stickSeq = 0;
  const stickParams: StickControlProtocol = {
    roll: STICK_MID,
    pitch: STICK_MID,
    throttle: STICK_MID,
    yaw: STICK_MID,
    gimbal_pitch: STICK_MID,
  };

  initParams();
  function initParams() {
    params.h =0;
    params.w = 0;
    params.x = 0;
    params.y = 0;
  }
  function handlePublish () {
    myInterval = setInterval(() => {
      if (params.y !=0 ||params.x != 0||params.w != 0|| params.h != 0) {
        const body = {
          method: DRC_METHOD.DRONE_CONTROL,
          data: params,
        }
        body.data.seq = seq++
        seq++
        // window.console.log('keyCode>>>>', activeCodeKey.value, body,deviceTopicInfo.pubTopic);
        mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, { qos: 0 })
      }

    }, 100)
  }

  function handlePublish2 () {
    myInterval = setInterval(() => {
      const isNeutral = stickParams.roll === STICK_MID &&
                        stickParams.pitch === STICK_MID &&
                        stickParams.throttle === STICK_MID &&
                        stickParams.yaw === STICK_MID &&
                        stickParams.gimbal_pitch === STICK_MID;

      if (!isNeutral) {
        const body = {
          seq: stickSeq++,
          method: DRC_METHOD.STICK_CONTROL,
          data: stickParams,
        }
        mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, { qos: 0 })
      }
    }, 200)
  }

  // 红外联动变焦 str 负载编号，state开关
  function drcLinkageZoomSet(str: string, state: number) {
    const body = {
      method: DRC_METHOD.DRC_LINKAGE_ZOOM_SET,
      data: {
        payload_index: str,
        state: state
      },
      seq: zoomSetSeq++
    }
    mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, {qos: 0})
  }

  //设置定时拍
  function drcIntervalPhotoSet(payload_index:string,interval:number) {
    const data = {
      payload_index:payload_index,
      interval : interval
    };
    const body = {
      method: DRC_METHOD.DRC_INTERVAL_PHOTO_SET,
      data: data,
      seq: drcIntervalPhotoSetSeq++
    }
    mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, { qos: 0 })
  }

  function handleKeyup (keyCode: KeyCode) {
    if (!deviceTopicInfo.pubTopic) {
      message.error('请确保已经建立DRC链路')
      return
    }
    const SPEED = 3 //  check
    const HEIGHT = 3 //  check
    const W_SPEED = 10 // 机头角速度
    switch (keyCode) {
      case 'KeyA':
        params.y =  -SPEED ;
        break
      case 'KeyW':
        params.x =  SPEED;
        break
      case 'KeyS':
        params.x =  -SPEED;
        break
      case 'KeyD':
        params.y =  SPEED;
        break
      case 'ArrowUp':
        params.h=  HEIGHT ;
        break
      case 'ArrowDown':
        params.h =  -HEIGHT;
        break
      case 'KeyQ':
        params.w =  -W_SPEED
        break
      case 'KeyE':
        params.w = W_SPEED ;
        break
      default:
        break
    }
  }

  function handleKeyup2 (keyCode: KeyCode) {
    if (!deviceTopicInfo.pubTopic) {
      message.error('请确保已经建立DRC链路')
      return
    }
    switch (keyCode) {
      case 'KeyA': // left -> roll-
        stickParams.roll = STICK_MID - STICK_OFFSET;
        break;
      case 'KeyD': // right -> roll+
        stickParams.roll = STICK_MID + STICK_OFFSET;
        break;
      case 'KeyW': // forward -> pitch+
        stickParams.pitch = STICK_MID + STICK_OFFSET;
        break;
      case 'KeyS': // backward -> pitch-
        stickParams.pitch = STICK_MID - STICK_OFFSET;
        break;
      case 'ArrowUp': // up -> throttle+
        stickParams.throttle = STICK_MID + STICK_OFFSET;
        break;
      case 'ArrowDown': // down -> throttle-
        stickParams.throttle = STICK_MID - STICK_OFFSET;
        break;
      case 'KeyQ': // rotate left -> yaw-
        stickParams.yaw = STICK_MID - STICK_OFFSET;
        break;
      case 'KeyE': // rotate right -> yaw+
        stickParams.yaw = STICK_MID + STICK_OFFSET;
        break;
      default:
        break;
    }
  }

  function handleClearInterval () {
    clearInterval(myInterval)
    myInterval = undefined
  }

  function resetControlState (keyCode: KeyCode) {
    switch (keyCode) {
      case 'KeyA':
        params.y =  0;
        break
      case 'KeyW':
        params.x =  0;
        break
      case 'KeyS':
        params.x =  0;
        break
      case 'KeyD':
        params.y =  0;
        break
      case 'ArrowUp':
        params.h= 0 ;
        break
      case 'ArrowDown':
        params.h =  0;
        break
      case 'KeyQ':
        params.w =  0;
        break
      case 'KeyE':
        params.w = 0 ;
        break
      default:
        break
    }
  }

  function resetControlState2 (keyCode: KeyCode) {
    switch (keyCode) {
      case 'KeyA':
      case 'KeyD':
        stickParams.roll = STICK_MID;
        break;
      case 'KeyW':
      case 'KeyS':
        stickParams.pitch = STICK_MID;
        break;
      case 'ArrowUp':
      case 'ArrowDown':
        stickParams.throttle = STICK_MID;
        break;
      case 'KeyQ':
      case 'KeyE':
        stickParams.yaw = STICK_MID;
        break;
      default:
        break;
    }
  }

  function onKeyup (e: KeyboardEvent) {
    console.log("keyCodeup"+e.code as KeyCode)
    if (useStickControl) {
      resetControlState2(e.code as KeyCode)
    } else {
      resetControlState(e.code as KeyCode)
    }
  }

  function onKeydown (e: KeyboardEvent) {
    if (useStickControl) {
      handleKeyup2(e.code as KeyCode)
    } else {
      handleKeyup(e.code as KeyCode)
    }
  }

  function startKeyboardManualControl () {
    if (useStickControl) {
      handlePublish2();
    } else {
      handlePublish();
    }
    window.addEventListener('keydown', onKeydown)
    window.addEventListener('keyup', onKeyup)
  }

  function closeKeyboardManualControl () {
    // resetControlState()
    handleClearInterval();
    window.removeEventListener('keydown', onKeydown)
    window.removeEventListener('keyup', onKeyup)
  }

  watch(() => isCurrentFlightController.value, (val) => {
    if (val && deviceTopicInfo.pubTopic) {
      startKeyboardManualControl()
    } else {
      closeKeyboardManualControl()
    }
  }, { immediate: true })

  onUnmounted(() => {
    closeKeyboardManualControl()
  })

  function handleEmergencyStop () {
    if (!deviceTopicInfo.pubTopic) {
      message.error('请确保已经建立DRC链路')
      return
    }
    const body = {
      method: DRC_METHOD.DRONE_EMERGENCY_STOP,
      data: {}
    }
    initParams();
    seq=0;
    window.console.log('handleEmergencyStop>>>>', deviceTopicInfo.pubTopic, body)
    mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, { qos: 1 })
  }

  return {
    activeCodeKey,
    handleKeyup,
    handleEmergencyStop,
    resetControlState,
    drcLinkageZoomSet,
    handleKeyup2,
    resetControlState2,
  }
}
