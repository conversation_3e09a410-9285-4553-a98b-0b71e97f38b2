<template>
  <div class="panel" style="padding-top: 5px;" :class="{ disable: !flightArea.status }">
    <div class="title">
      <a-tooltip :title="flightArea.name">
        <div class="pr10" style="white-space: nowrap;text-overflow: ellipsis; overflow: hidden;">{{ flightArea.name }}
        </div>
      </a-tooltip>
    </div>
    <div class="mt5 ml10" style="color: hsla(0,0%,100%,0.35);">
      <span class="mr10">更新于 {{ formatDateTime(flightArea.update_time).toLocaleString() }}</span>
    </div>
    <div class="flex-row flex-justify-between flex-align-center ml10 mt5" style="color: hsla(0,0%,100%,0.65);">
      <FlightAreaIcon :type="flightArea.type" :isCircle="EGeometryType.CIRCLE === flightArea.content.geometry.type" />
      <div class="mr10 operate">
        <ConfigProvider :locale="zh_CN">
          <a-popconfirm v-if="flightArea.status" title="是否决定禁用当前区域?" okText="禁用" @confirm="changeAreaStatus(false)">
            <stop-outlined />
          </a-popconfirm>
          <a-popconfirm v-else @confirm="changeAreaStatus(true)" title="是否决定启用当前区域?">
            <check-circle-outlined />
          </a-popconfirm>
        </ConfigProvider>
        <EnvironmentFilled class="ml10" @click="clickLocation" />
        <ConfigProvider :locale="zh_CN">
          <a-popconfirm title="是否决定删除当前区域?" okText="删除" okType="danger" @confirm="deleteArea">
            <delete-outlined class="ml10" />
          </a-popconfirm>
        </ConfigProvider>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, reactive, defineEmits, computed } from 'vue'
import { GetFlightArea, changeFlightAreaStatus } from '../../api/flight-area'
import FlightAreaIcon from './FlightAreaIcon.vue'
import { formatDateTime } from '../../utils/time'
import { EGeometryType } from '../../types/flight-area'
import { useGMapCover } from '@/hooks/use-g-map-cover'
import { useFlightArea } from '@/components/flight-area/use-flight-area'
import zh_CN from 'ant-design-vue/es/locale/zh_CN';
import { ConfigProvider } from 'ant-design-vue'
import { StopOutlined, CheckCircleOutlined, DeleteOutlined, EnvironmentFilled } from '@ant-design/icons-vue'
let useGMapCoverHook = useGMapCover()

const props = defineProps<{
  data: GetFlightArea
}>()
const emit = defineEmits(['delete', 'update', 'location'])

const flightArea = computed(() => props.data)
 useFlightArea()

const changeAreaStatus = (status: boolean) => {
  changeFlightAreaStatus(props.data.area_id, status).then(res => {
    if (res.code === 0) {
      flightArea.value.status = status
      emit('update', flightArea)
    }
  })
}

const deleteArea = () => {
  emit('delete', flightArea.value.area_id)
  useGMapCoverHook.removeCoverFromMap(flightArea.value.area_id)

}
const clickLocation = () => {
  emit('location', flightArea.value.area_id)
}

</script>

<style lang="scss" scoped>
.panel {
  background: #192a3b;
  margin-left: auto;
  margin-right: auto;
  margin-top: 10px;
  height: 90px;
  width: 95%;
  font-size: 13px;
  border-radius: 2px;
  cursor: pointer;

  .title {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 30px;
    font-weight: bold;
    margin: 0px 10px 0 10px;
  }

  .operate>* {
    font-size: 16px;
  }
}

.disable {
  opacity: 50%;
}
</style>
