<template>
  <div class="left-sider">
    <arrow-left-outlined class="icon-back" @click="goBack" />
    <a-tooltip v-for="item in iconList" :key="item.path" placement="right" :title="item.title">
      <div class="icon-item" :class="{ active: isActive(item.path) }" @click="router.push(item.navPath)">
        <component :is="item.icon" />
      </div>
    </a-tooltip>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { ArrowLeftOutlined, NodeIndexOutlined, ApartmentOutlined, CalendarOutlined, InboxOutlined, CompassOutlined } from '@ant-design/icons-vue';
import { Tooltip as ATooltip } from 'ant-design-vue';

const router = useRouter();
const route = useRoute();

const isActive = (path) => {
  return route.path.startsWith(path);
};

const goBack = () => {
  router.push('/workspace/index');
};

const iconList = [
  {
    icon: ApartmentOutlined,
    path: '/project',
    navPath: '/project/index',
    title: '项目'
  },
  {
    icon: NodeIndexOutlined,
    path: '/workspace/wayline',
    navPath: '/workspace/wayline',
    title: '航线'
  },
  {
    icon: CalendarOutlined,
    path: '/workspace/task',
    navPath: '/workspace/task',
    title: '任务'
  },
  {
    icon: InboxOutlined,
    path: '/device/index',
    navPath: '/device/index',
    title: '设备'
  },
  {
    icon: CompassOutlined,
    path: '/workMap/index',
    navPath: '/workMap/index',
    title: '作业地图'
  },
];
</script>

<style lang="scss" scoped>
.left-sider {
  width: 50px;
  flex-shrink: 0;
  height: 100%;
  background-color: #232323;
  border-right: 1px solid #3c3c3c;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;

  .icon-back,
  .icon-item {
    margin-bottom: 20px;
    font-size: 18px;
    cursor: pointer;
    color: #e0e0e0;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      color: #fff;
    }

    &.active {
      background-color: #000;
      color: #1890ff;
    }
  }
}
</style>