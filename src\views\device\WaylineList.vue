<template>
    <basic-container>
        <div class="wayline-toolbar">
            <div class="return-height">
                <span class="return-height-label">返航高度(米)：</span>
                <el-input v-model="returnHeight" placeholder="返航高度" class="return-height-input" />
            </div>
        </div>
        <avue-crud :option="option" :table-loading="loading" :data="data" v-model:page="page" ref="crud"
            @row-del="rowDel" v-model="form" :permission="permissionList" @row-update="rowUpdate" @row-save="rowSave"
            :before-open="beforeOpen" @search-change="searchChange" @search-reset="searchReset"
            @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
            @refresh-change="refreshChange" @on-load="onLoad" @row-click="rowClick">
            <template #radio="{ row }">
                <el-radio v-model="selectRow" :label="row.$index">{{ '' }}</el-radio>
            </template>
            <template #device_model_key="{ row }">
                <p class="item-desc">{{ DroneModelEnum[row.device_model_key] || '未知机型' }}</p>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import { getWayLineList as getList } from '@/api/workspace';
import { mapGetters } from 'vuex';
import { DroneModelEnum } from '@/constants/drone';

export default {
    props: {
        dockSn: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            returnHeight: 100,
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 32,
                dialogWidth: 950,
                tip: false,
                searchShow: true,
                searchMenuSpan: 6,
                border: true,
                index: false,
                selection: false,
                excelBtn: true,
                delBtn: false,
                addBtn: false,
                editBtn: false,
                dialogClickModal: false,
                grid: false,
                labelWidth: 120,
                column: [
                    { label: '', prop: 'radio', width: 60, hide: false },
                    {
                        label: '航线名称',
                        prop: 'name',
                        display: false,
                    },
                    {
                        label: '设备名称',
                        prop: 'device_model_key',
                        display: false,
                    },
                ],
            },
            data: [],
            selectRow: '',
            DroneModelEnum,
        };
    },
    computed: {
        ...mapGetters(['permission']),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.notice_add, false),
                viewBtn: this.validData(this.permission.notice_view, false),
                delBtn: this.validData(this.permission.notice_delete, false),
                editBtn: this.validData(this.permission.notice_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(',');
        },
    },
    methods: {
        rowClick(row) {
            this.selectRow = row.$index;
        },
        getSelectedRow() {
            if (this.selectRow !== '' && this.data[this.selectRow]) {
                return this.data[this.selectRow];
            }
            return null;
        },
        rowSave(row, done, loading) {
            add(row).then(
                () => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    done();
                },
                error => {
                    window.console.log(error);
                    loading();
                }
            );
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(
                () => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    done();
                },
                error => {
                    window.console.log(error);
                    loading();
                }
            );
        },
        rowDel(row) {
            this.$confirm('确定将选择数据删除?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                });
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning('请选择至少一条数据');
                return;
            }
            this.$confirm('确定将选择数据删除?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (['edit', 'view'].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            let values = {
                dockSn: this.dockSn,
                ...params,
                ...this.query,
            };
            this.loading = true;
            getList(page.currentPage, page.pageSize, values).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data;
                this.loading = false;
                this.selectionClear();
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.wayline-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.return-height {
    display: flex;
    align-items: center;
    
    &-label {
        margin-right: 8px;
        white-space: nowrap;
        font-size: 14px;
        color: #606266;
    }
    
    &-input {
        width: 120px;
    }
}
</style>