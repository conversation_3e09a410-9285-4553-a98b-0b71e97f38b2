<template>
    <div class="device-box">
        <left-sider />
        <basic-container style="flex: 1;">
            <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                <el-tab-pane label="机场" name="gateway"></el-tab-pane>
                <el-tab-pane label="飞行器" name="drone"></el-tab-pane>
            </el-tabs>
            <avue-crud :option="option" :table-loading="loading" :data="data" v-model:page="page" ref="crud"
                @row-del="rowDel" v-model="form" :permission="permissionList" @row-update="rowUpdate"
                @row-save="rowSave" :before-open="beforeOpen" @search-change="searchChange" @search-reset="searchReset"
                @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
                @refresh-change="refreshChange" @on-load="onLoad">
                <template #menu="{ size, row, index }">
                    <el-button v-if="activeName === 'gateway'" type="primary" text icon="el-icon-link" plain
                        @click="bindWayline(row)">绑定航线</el-button>
                </template>
            </avue-crud>
        </basic-container>
        <el-dialog title="绑定航线" v-model="showBindWaylineDialog" :append-to-body="true" width="80%">
            <bind-wayline v-if="showBindWaylineDialog" :dockSn="currentSn" />
        </el-dialog>
    </div>
</template>

<script>
import { getList, remove, update, add, getDetail } from '@/api/device/index';
import { mapGetters } from 'vuex';
import LeftSider from '@/components/left-sider/main.vue';
import bindWayline from './bindWayline.vue';

export default {
    components: {
        LeftSider,
        bindWayline,
    },
    data() {
        return {
            showBindWaylineDialog: false,
            currentSn: '',
            activeName: 'gateway',
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 32,
                dialogWidth: 950,
                tip: false,
                searchShow: true,
                searchMenuSpan: 6,
                border: true,
                index: false,
                selection: false,
                excelBtn: true,
                delBtn: false,
                addBtn: false,
                dialogClickModal: false,
                grid: false,
                labelWidth: 120,
                column: [
                    {
                        label: '设备型号',
                        prop: 'deviceModel',
                        display: false,
                        rules: [
                            {
                                required: true,
                                message: '请输入设备型号',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '设备SN',
                        prop: 'sn',
                        display: false,
                        rules: [
                            {
                                required: true,
                                message: '请输入设备SN',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '设备名称',
                        prop: 'callsign',
                        display: false,
                        rules: [
                            {
                                required: true,
                                message: '请输入设备名称',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '在线状态',
                        prop: 'deviceOnlineStatus',
                        display: false,
                        dicData: [
                            {
                                label: '在线',
                                value: true,
                            },
                            {
                                label: '离线',
                                value: false,
                            },
                        ],
                        rules: [
                            {
                                required: true,
                                message: '请选择在线状态',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '设备状态',
                        prop: 'modeCode',
                        display: false,
                        rules: [
                            {
                                required: true,
                                message: '请选择设备状态',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '经度',
                        prop: 'longitude',
                        rules: [
                            {
                                required: true,
                                message: '请输入经度',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '维度',
                        prop: 'latitude',
                        rules: [
                            {
                                required: true,
                                message: '请输入维度',
                                trigger: 'blur',
                            },
                        ],
                    },
                    {
                        label: '作业范围(公里)',
                        prop: 'operationRangeKm',
                        rules: [
                            {
                                required: true,
                                message: '请输入作业范围',
                                trigger: 'blur',
                            },
                        ],
                    },
                ],
            },
            data: [],
        };
    },
    computed: {
        ...mapGetters(['permission']),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.notice_add, false),
                viewBtn: this.validData(this.permission.notice_view, false),
                delBtn: this.validData(this.permission.notice_delete, false),
                editBtn: this.validData(this.permission.notice_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(',');
        },
    },
    watch: {
        activeName(val) {
            this.onLoad(this.page);
        },
    },
    methods: {
        bindWayline(row) {
            this.currentSn = row.sn;
            this.showBindWaylineDialog = true;
        },
        rowSave(row, done, loading) {
            add(row).then(
                () => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    done();
                },
                error => {
                    window.console.log(error);
                    loading();
                }
            );
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(
                () => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    done();
                },
                error => {
                    window.console.log(error);
                    loading();
                }
            );
        },
        rowDel(row) {
            this.$confirm('确定将选择数据删除?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                });
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning('请选择至少一条数据');
                return;
            }
            this.$confirm('确定将选择数据删除?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: 'success',
                        message: '操作成功!',
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (['edit', 'view'].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            params.type = this.activeName;
            let values = {
                ...params,
                ...this.query,
            };
            this.loading = true;
            getList(page.currentPage, page.pageSize, values).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records.map(item => {
                    return {
                        ...item,
                        deviceModel: JSON.parse(item.deviceModel).name,
                    };
                });
                this.loading = false;
                this.selectionClear();
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.device-box {
    height: 100%;
    display: flex;
}
</style>