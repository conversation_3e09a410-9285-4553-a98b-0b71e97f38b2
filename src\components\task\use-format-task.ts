import { DEFAULT_PLACEHOLDER } from '@/utils/constants'
import { Task } from '@/api/wayline'
import { TaskStatusColor, TaskStatusMap, TaskTypeMap, OutOfControlActionMap, MediaStatusMap, MediaStatusColorMap, MediaStatus, WaylineTypeMap, TaskStatus, TaskTypeStringMap } from '@/types/task'
import { isNil } from 'lodash'

export function useFormatTask() {
  function formatTaskType(task: Task) {
    if (typeof task.task_type === 'number') {
      return TaskTypeMap[task.task_type]
    }
    return TaskTypeStringMap[task.task_type] || task.task_type
  }

  function formatTaskDate(time: string) {
    if (!time) {
      return ' '
    }
    const date = new Date(time)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  function formatTaskTime(time: string) {
    if (!time) {
      return DEFAULT_PLACEHOLDER
    }
    const date = new Date(time)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  function formatLostAction(task: Task) {
    return OutOfControlActionMap[task.out_of_control_action] || DEFAULT_PLACEHOLDER
  }

  function formatTaskStatus(task: Task) {
    const statusObj = {
      text: '',
      color: ''
    }
    const { status } = task
    // HACK: The status from the backend is a string, but the type definition is a number enum.
    const numericStatus = TaskStatus[status as any as keyof typeof TaskStatus]
    statusObj.text = TaskStatusMap[numericStatus]
    statusObj.color = TaskStatusColor[numericStatus]
    return statusObj
  }

  function formatMediaTaskStatus(task: Task) {
    const statusObj = {
      text: '',
      color: '',
      number: '',
      status: MediaStatus.Empty,
    }
    statusObj.text = MediaStatusMap[task.media_upload_status]
    statusObj.color = MediaStatusColorMap[task.media_upload_status]
    statusObj.status = task.media_upload_status as MediaStatus
    // const { media_count, uploaded_count, uploading } = task
    // if (isNil(media_count) || isNaN(media_count)) {
    //   return statusObj
    // }
    // const expectedFileCount = media_count || 0
    // const uploadedFileCount = uploaded_count || 0
    // if (media_count === 0) {
    //   statusObj.text = MediaStatusMap[MediaStatus.Empty]
    //   statusObj.color = MediaStatusColorMap[MediaStatus.Empty]
    // } else if (media_count === uploaded_count) {
    //   statusObj.text = MediaStatusMap[MediaStatus.Success]
    //   statusObj.color = MediaStatusColorMap[MediaStatus.Success]
    //   statusObj.number = `(${uploadedFileCount}/${expectedFileCount})`
    //   statusObj.status = MediaStatus.Success
    // } else {
    //   if (uploading) {
    //     statusObj.text = MediaStatusMap[MediaStatus.Uploading]
    //     statusObj.color = MediaStatusColorMap[MediaStatus.Uploading]
    //     statusObj.status = MediaStatus.Uploading
    //   } else {
    //     statusObj.text = MediaStatusMap[MediaStatus.ToUpload]
    //     statusObj.color = MediaStatusColorMap[MediaStatus.ToUpload]
    //     statusObj.status = MediaStatus.ToUpload
    //   }
    //   statusObj.number = `(${uploadedFileCount}/${expectedFileCount})`
    // }
    return statusObj
  }

  function formatWaylineType(task: Task) {
    const waylineType = Number(task.wayline_type);
    return WaylineTypeMap[waylineType] || DEFAULT_PLACEHOLDER
  }

  function formatTemplateTypes(task: Task) {
    const waylineType = Number(task.wayline_type);
    return WaylineTypeMap[waylineType] || DEFAULT_PLACEHOLDER
  }

  return {
    formatTaskType,
    formatTaskDate,
    formatTaskTime,
    formatLostAction,
    formatTaskStatus,
    formatMediaTaskStatus,
    formatWaylineType,
    formatTemplateTypes
  }
}
