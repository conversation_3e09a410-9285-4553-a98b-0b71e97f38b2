define(["./Transforms-01e95659","./Matrix3-a348023f","./ComponentDatatype-77274976","./defaultValue-0a909f67","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryOffsetAttribute-04332ce7","./Math-e97915da","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./WebGLConstants-a8cc3e8c"],(function(t,e,n,a,i,r,u,o,s,m,f,c){"use strict";const p=new e.Cartesian3;function d(t){const n=(t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT)).minimum,i=t.maximum;this._min=e.Cartesian3.clone(n),this._max=e.<PERSON><PERSON>3.clone(i),this._offsetAttribute=t.offsetAttribute,this._workerName="createBoxOutlineGeometry"}d.fromDimensions=function(t){const n=(t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT)).dimensions,i=e.Cartesian3.multiplyByScalar(n,.5,new e.Cartesian3);return new d({minimum:e.Cartesian3.negate(i,new e.Cartesian3),maximum:i,offsetAttribute:t.offsetAttribute})},d.fromAxisAlignedBoundingBox=function(t){return new d({minimum:t.minimum,maximum:t.maximum})},d.packedLength=2*e.Cartesian3.packedLength+1,d.pack=function(t,n,i){return i=a.defaultValue(i,0),e.Cartesian3.pack(t._min,n,i),e.Cartesian3.pack(t._max,n,i+e.Cartesian3.packedLength),n[i+2*e.Cartesian3.packedLength]=a.defaultValue(t._offsetAttribute,-1),n};const l=new e.Cartesian3,y=new e.Cartesian3,C={minimum:l,maximum:y,offsetAttribute:void 0};return d.unpack=function(t,n,i){n=a.defaultValue(n,0);const r=e.Cartesian3.unpack(t,n,l),u=e.Cartesian3.unpack(t,n+e.Cartesian3.packedLength,y),o=t[n+2*e.Cartesian3.packedLength];return a.defined(i)?(i._min=e.Cartesian3.clone(r,i._min),i._max=e.Cartesian3.clone(u,i._max),i._offsetAttribute=-1===o?void 0:o,i):(C.offsetAttribute=-1===o?void 0:o,new d(C))},d.createGeometry=function(o){const s=o._min,m=o._max;if(e.Cartesian3.equals(s,m))return;const f=new r.GeometryAttributes,c=new Uint16Array(24),d=new Float64Array(24);d[0]=s.x,d[1]=s.y,d[2]=s.z,d[3]=m.x,d[4]=s.y,d[5]=s.z,d[6]=m.x,d[7]=m.y,d[8]=s.z,d[9]=s.x,d[10]=m.y,d[11]=s.z,d[12]=s.x,d[13]=s.y,d[14]=m.z,d[15]=m.x,d[16]=s.y,d[17]=m.z,d[18]=m.x,d[19]=m.y,d[20]=m.z,d[21]=s.x,d[22]=m.y,d[23]=m.z,f.position=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d}),c[0]=4,c[1]=5,c[2]=5,c[3]=6,c[4]=6,c[5]=7,c[6]=7,c[7]=4,c[8]=0,c[9]=1,c[10]=1,c[11]=2,c[12]=2,c[13]=3,c[14]=3,c[15]=0,c[16]=0,c[17]=4,c[18]=1,c[19]=5,c[20]=2,c[21]=6,c[22]=3,c[23]=7;const l=e.Cartesian3.subtract(m,s,p),y=.5*e.Cartesian3.magnitude(l);if(a.defined(o._offsetAttribute)){const t=d.length,e=o._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(t/3).fill(e);f.applyOffset=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new i.Geometry({attributes:f,indices:c,primitiveType:i.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(e.Cartesian3.ZERO,y),offsetAttribute:o._offsetAttribute})},function(t,e){return a.defined(e)&&(t=d.unpack(t,e)),d.createGeometry(t)}}));
