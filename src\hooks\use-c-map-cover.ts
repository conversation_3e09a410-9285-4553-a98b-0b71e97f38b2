import { getRoot } from '@/root'
import { EFlightAreaType } from '../types/flight-area'
import pin19be6b from '@/assets/pin-19be6b.svg'
import pin212121 from '@/assets/pin-212121.svg'
import pin2d8cf0 from '@/assets/pin-2d8cf0.svg'
import pinb620e0 from '@/assets/pin-b620e0.svg'
import pine23c39 from '@/assets/pin-e23c39.svg'
import pineffbb00 from '@/assets/pin-ffbb00.svg'
import rootStore from '@/store'
import { GeojsonCoordinate } from '@/types/map'
import { gcj02towgs84, wgs84togcj02 } from '@/vendors/coordtransform';
import EventBus from '@/event-bus';

export function useGMapCover() {
  const root = getRoot()
  const normalColor = '#2D8CF0'
  const store = rootStore
  const coverMap = store.state.dock.coverMap
  const flightAreaColorMap = {
    [EFlightAreaType.DFENCE]: '#19be6b',
    [EFlightAreaType.NFZ]: '#ff0000',
  }
  const disableColor = '#b3b3b3'

  // 添加覆盖物到地图
  function AddCoverToMap(cover: any) {
    const viewer = root.$viewer
    // 在Cesium中，实体已经添加到viewer.entities中
    coverMap[cover.id] = [cover]
  }

  // 获取图标颜色
  function getPinIcon(color?: string) {
    const colorObj: {
      [key: string]: any
    } = {
      '2d8cf0': pin2d8cf0,
      '19be6b': pin19be6b,
      '212121': pin212121,
      'b620e0': pinb620e0,
      'e23c39': pine23c39,
      'ffbb00': pineffbb00,
    }
    const iconName = (color?.replace(/#/g, '') || '').toLowerCase()

    try {
      // 检查是否存在此颜色的图标，不存在则使用默认图标
      return colorObj[iconName] || pin2d8cf0
    } catch (e) {
      console.warn('图标加载失败，使用默认图标', e)
      return pin2d8cf0
    }
  }

  // 初始化2D点标记
  function init2DPin(name: string, coordinates: GeojsonCoordinate, color?: string, data?: any) {
    console.log('init2DPin', name, coordinates, color, data);

    const viewer = root.$viewer
    const entityId = data?.id || generateId()
    const borderColor = color || normalColor

    // 创建一个棱形的canvas图标
    const canvas = document.createElement('canvas');
    canvas.width = 30;
    canvas.height = 40; // 增加高度使棱形更细长
    const context = canvas.getContext('2d');
    if (context) {
      // 绘制细长的棱形
      context.clearRect(0, 0, canvas.width, canvas.height);
      context.beginPath();
      context.moveTo(15, 0);    // 顶点
      context.lineTo(25, 20);   // 右侧点
      context.lineTo(15, 40);   // 底部点
      context.lineTo(5, 20);    // 左侧点
      context.closePath();

      // 设置边框颜色
      context.strokeStyle = borderColor;
      context.lineWidth = 2;
      context.stroke();

      // 设置中空
      context.fillStyle = 'rgba(0, 0, 0, 0)';
      context.fill();
    }

    const entity = viewer.entities.add({
      id: entityId,
      position: Cesium.Cartesian3.fromDegrees(coordinates[0], coordinates[1]),
      name: name,
      billboard: {
        image: canvas,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        scale: 0.4,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        sizeInMeters: true
      },
      label: {
        text: name,
        font: '16px sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(15, 0),
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      },
      properties: data ? {
        ...data,
        type: 'pin'
      } : { type: 'pin' }
    })

    AddCoverToMap(entity)
  }

  // 生成唯一ID
  function generateId() {
    return 'entity-' + Math.random().toString(36).substr(2, 9)
  }

  // 添加覆盖物组
  function AddOverlayGroup(overlayGroup: any) {
    const id = overlayGroup.id || generateId()
    coverMap[id] = [...(coverMap[id] || []), overlayGroup]
  }

  // 初始化线
  function initPolyline(name: string, coordinates: GeojsonCoordinate[], color?: string, data?: any) {
    const viewer = root.$viewer
    const positions = coordinates.map(coordinate =>
      Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
    )

    const entityId = data?.id || generateId()
    const entity = viewer.entities.add({
      id: entityId,
      name: name,
      polyline: {
        positions: positions,
        width: 2,
        material: Cesium.Color.fromCssColorString(color || normalColor),
        clampToGround: true
      },
      properties: data ? {
        ...data,
        type: 'polyline'
      } : { type: 'polyline' }
    })

    // 添加点击事件处理
    if (data) {
      // 使用点击事件处理
      viewer.screenSpaceEventHandler.setInputAction((movement: any) => {
        const pickedObject = viewer.scene.pick(movement.position)
        if (Cesium.defined(pickedObject) && pickedObject.id === entity) {
          // @ts-ignore
          EventBus.emit('lineClick', data)
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    }

    AddOverlayGroup(entity)
  }

  // 初始化多边形
  function initPolygon(name: string, coordinates: GeojsonCoordinate[][], color?: string, data?: any) {
    const viewer = root.$viewer
    const positions = coordinates[0].map(coordinate =>
      Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
    )

    const entityId = data?.id || generateId()
    const entity = viewer.entities.add({
      id: entityId,
      name: name,
      polygon: {
        hierarchy: new Cesium.PolygonHierarchy(positions),
        material: Cesium.Color.fromCssColorString(color || normalColor).withAlpha(0.4),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(color || normalColor),
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      properties: data ? {
        ...data,
        type: 'polygon'
      } : { type: 'polygon' }
    })

    AddOverlayGroup(entity)
  }

  // 从地图中移除覆盖物
  function removeCoverFromMap(id: string) {
    const viewer = root.$viewer
    if (coverMap[id]) {
      coverMap[id].forEach(cover => viewer.entities.remove(cover))
      coverMap[id] = []
    }

    // 移除可能存在的轮廓实体
    const outlineEntity = viewer.entities.getById(id + '-outline')
    if (outlineEntity) {
      viewer.entities.remove(outlineEntity)
    }

    // 移除对应的文本标签
    const textEntity = viewer.entities.getById(id + '-text')
    if (textEntity) {
      viewer.entities.remove(textEntity)
    }
  }

  // 从地图获取元素
  function getElementFromMap(id: string): any[] {
    return coverMap[id] || []
  }

  // 更新点标记元素
  function updatePinElement(id: string, name: string, coordinates: GeojsonCoordinate, color?: string, textType?: string) {
    const viewer = root.$viewer
    const elements = getElementFromMap(id)

    if (elements && elements.length > 0 && textType) {
      const element = elements[0]
      element.position = Cesium.Cartesian3.fromDegrees(coordinates[0], coordinates[1])
      element.name = name

      // 如果需要更新颜色，重新生成canvas图标
      if (element.billboard && color) {
        const borderColor = color || normalColor

        const canvas = document.createElement('canvas');
        canvas.width = 30;
        canvas.height = 40; // 增加高度使棱形更细长
        const context = canvas.getContext('2d');
        if (context) {
          // 绘制细长的棱形
          context.clearRect(0, 0, canvas.width, canvas.height);
          context.beginPath();
          context.moveTo(15, 0);    // 顶点
          context.lineTo(25, 20);   // 右侧点
          context.lineTo(15, 40);   // 底部点
          context.lineTo(5, 20);    // 左侧点
          context.closePath();

          // 设置边框颜色
          context.strokeStyle = borderColor;
          context.lineWidth = 2;
          context.stroke();

          // 设置中空
          context.fillStyle = 'rgba(0, 0, 0, 0)';
          context.fill();
        }

        element.billboard.image = canvas;
      }
    } else {
      init2DPin(name, coordinates, color, {
        id: id,
        name: name
      })
    }
  }

  // 更新线元素
  function updatePolylineElement(id: string, name: string, coordinates: GeojsonCoordinate[], color?: string, textType?: string) {
    const viewer = root.$viewer
    const elements = getElementFromMap(id)

    if (elements && elements.length > 0 && textType) {
      const element = elements[0]
      if (element.polyline) {
        element.polyline.material = Cesium.Color.fromCssColorString(color || normalColor)
      }
    } else {
      initPolyline(name, coordinates, color, {
        id: id,
        name: name
      })
    }
  }

  // 更新多边形元素
  function updatePolygonElement(id: string, name: string, coordinates: GeojsonCoordinate[][], color?: string, textType?: string) {
    const viewer = root.$viewer
    const elements = getElementFromMap(id)

    if (elements && elements.length > 0 && textType) {
      const element = elements[0]
      if (element.polygon) {
        element.polygon.material = Cesium.Color.fromCssColorString(color || normalColor).withAlpha(0.4)
        element.polygon.outlineColor = Cesium.Color.fromCssColorString(color || normalColor)
      }
    } else {
      initPolygon(name, coordinates, color, {
        id: id,
        name: name
      })
    }
  }

  // 清除文本信息
  function clearTextInfo(id: string) {
    const viewer = root.$viewer

    // 先检查并移除指定ID的text_前缀实体
    const textEntityId = `text_${id}`
    const textEntity = viewer.entities.getById(textEntityId)
    if (textEntity) {
      viewer.entities.remove(textEntity)
    }

    // 再检查并处理coverMap中的元素
    const elements = getElementFromMap(id)
    if (elements && elements.length > 0) {
      const textElement = elements.find(ele => ele.properties?.type === 'text')
      if (textElement) {
        viewer.entities.remove(textElement)
        const index = elements.indexOf(textElement)
        elements.splice(index, 1)

        if (elements.length === 0) {
          delete coverMap[id]
        } else {
          coverMap[id] = elements
        }
      }
    }
  }

  // 初始化文本标注信息
  function initTextInfo(name: string, coordinates: GeojsonCoordinate, id: string) {
    try {
      // 先清除可能存在的同ID文本实体
      clearTextInfo(id);

      // 验证坐标是否有效
      if (!coordinates || !Array.isArray(coordinates) || coordinates.length < 2) {
        console.warn('标注坐标无效', coordinates);
        return;
      }

      // 将coordinates转换为二维数组，如果它不是的话
      let validCoordinates: GeojsonCoordinate;
      if (Array.isArray(coordinates[0]) && coordinates[0].length >= 2) {
        validCoordinates = [coordinates[0][0], coordinates[0][1]] as GeojsonCoordinate;
      } else if (coordinates.length >= 2) {
        validCoordinates = [coordinates[0], coordinates[1]] as GeojsonCoordinate;
      } else {
        console.warn('无法处理的坐标格式', coordinates);
        return;
      }

      // 确保坐标值为数字
      if (isNaN(Number(validCoordinates[0])) || isNaN(Number(validCoordinates[1]))) {
        console.warn('坐标值不是有效数字', validCoordinates);
        return;
      }

      const viewer = root.$viewer;
      const entity = viewer.entities.add({
        id: `text_${id}`,
        position: Cesium.Cartesian3.fromDegrees(Number(validCoordinates[0]), Number(validCoordinates[1])),
        label: {
          text: name,
          font: '16px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });

      // 将文本实体添加到覆盖物管理中
      AddCoverToMap(entity);
    } catch (error) {
      console.error('初始化文本标注失败:', error, name, coordinates, id);
    }
  }

  // 清除所有文本信息
  function clearAllTextInfo() {
    for (const id in coverMap) {
      clearTextInfo(id)
    }
  }

  // 初始化飞行区域圆
  function initFlightAreaCircle(name: string, radius: number, position: GeojsonCoordinate, data: { id: string, type: EFlightAreaType, enable: boolean }) {
    console.log(name, radius, position, data);

    const viewer = root.$viewer
    const circleEntity = viewer.entities.add({
      id: data.id,
      name: name,
      position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
      ellipse: {
        semiMinorAxis: radius,
        semiMajorAxis: radius,
        material: Cesium.Color.fromCssColorString(data.enable ? flightAreaColorMap[data.type] : disableColor).withAlpha(0),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(data.enable ? flightAreaColorMap[data.type] : disableColor).withAlpha(1.0),
        outlineWidth: 16,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        classificationType: Cesium.ClassificationType.BOTH,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, Number.MAX_VALUE),
        shadows: Cesium.ShadowMode.DISABLED
      },
      properties: {
        ...data,
        type: 'flightAreaCircle'
      }
    })

    // 添加额外的仅用于轮廓的实体
    const outlineEntity = viewer.entities.add({
      id: data.id + '-outline',
      position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
      ellipse: {
        semiMinorAxis: radius,
        semiMajorAxis: radius,
        fill: false,
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(data.enable ? flightAreaColorMap[data.type] : disableColor).withAlpha(1.0),
        outlineWidth: 3,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        classificationType: Cesium.ClassificationType.BOTH,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, Number.MAX_VALUE)
      },
      properties: {
        id: data.id,
        type: 'flightAreaCircleOutline'
      }
    })

    AddOverlayGroup(circleEntity)
    AddOverlayGroup(outlineEntity)
    initTextInfo(name + `\n直径：${radius * 2}米`, position, data.id)
  }

  // 更新飞行区域圆
  function updateFlightAreaCircle(id: string, name: string, radius: number, position: GeojsonCoordinate, enable: boolean, type: EFlightAreaType, show?: string) {
    const viewer = root.$viewer
    const elements = getElementFromMap(id)

    if (elements && elements.length > 0 && show !== "show") {
      let textElement = elements.find(ele => ele.properties?.getValue()?.type === 'text')
      let circleElement = elements.find(ele => ele.properties?.getValue()?.type === 'flightAreaCircle')
      let outlineElement = elements.find(ele => ele.properties?.getValue()?.type === 'flightAreaCircleOutline')

      if (!textElement) {
        initTextInfo(name + `\n直径：${radius * 2}米`, position, id)
      } else {
        textElement.position = Cesium.Cartesian3.fromDegrees(position[0], position[1])
        if (textElement.label) {
          textElement.label.text = name + `\n直径：${radius * 2}米`
        }
      }

      if (circleElement) {
        circleElement.position = Cesium.Cartesian3.fromDegrees(position[0], position[1])
        circleElement.ellipse.semiMinorAxis = radius
        circleElement.ellipse.semiMajorAxis = radius
        circleElement.ellipse.material = Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(0)
        circleElement.ellipse.outlineColor = Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(1.0)
        circleElement.ellipse.outlineWidth = 10
        circleElement.ellipse.classificationType = Cesium.ClassificationType.BOTH
        circleElement.ellipse.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0.0, Number.MAX_VALUE)
      }

      if (outlineElement) {
        outlineElement.position = Cesium.Cartesian3.fromDegrees(position[0], position[1])
        outlineElement.ellipse.semiMinorAxis = radius
        outlineElement.ellipse.semiMajorAxis = radius
        outlineElement.ellipse.outlineColor = Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(1.0)
      } else {
        // 如果没有轮廓实体，创建一个
        const outlineEntity = viewer.entities.add({
          id: id + '-outline',
          position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
          ellipse: {
            semiMinorAxis: radius,
            semiMajorAxis: radius,
            fill: false,
            outline: true,
            outlineColor: Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(1.0),
            outlineWidth: 3,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            classificationType: Cesium.ClassificationType.BOTH,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, Number.MAX_VALUE)
          },
          properties: {
            id: id,
            type: 'flightAreaCircleOutline'
          }
        })
        AddOverlayGroup(outlineEntity)
      }
    } else {
      initFlightAreaCircle(name, radius, position, { id, type, enable })
    }
  }

  // 计算多边形位置
  function calcPolygonPosition(coordinate: GeojsonCoordinate[]): GeojsonCoordinate {
    const index = coordinate.length - 1
    return [(coordinate[0][0] + coordinate[index][0]) / 2.0, (coordinate[0][1] + coordinate[index][1]) / 2]
  }

  // 初始化飞行区域多边形
  function initFlightAreaPolygon(name: string, coordinates: GeojsonCoordinate[], data: { id: string, type: EFlightAreaType, enable: boolean }) {
    const viewer = root.$viewer
    const positions = coordinates.map(coordinate =>
      Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
    )

    const polygonEntity = viewer.entities.add({
      id: data.id,
      name: name,
      polygon: {
        hierarchy: new Cesium.PolygonHierarchy(positions),
        material: Cesium.Color.fromCssColorString(data.enable ? flightAreaColorMap[data.type] : disableColor).withAlpha(0),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(data.enable ? flightAreaColorMap[data.type] : disableColor),
        outlineWidth: 8,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      properties: {
        ...data,
        type: 'flightAreaPolygon'
      }
    })

    // 添加额外的仅用于轮廓的实体
    const outlineEntity = viewer.entities.add({
      id: data.id + '-outline',
      polyline: {
        positions: [...positions, positions[0]], // 闭合多边形轮廓
        width: 3,
        material: Cesium.Color.fromCssColorString(data.enable ? flightAreaColorMap[data.type] : disableColor).withAlpha(1.0),
        clampToGround: true,
        classificationType: Cesium.ClassificationType.BOTH,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, Number.MAX_VALUE)
      },
      properties: {
        id: data.id,
        type: 'flightAreaPolygonOutline'
      }
    })

    AddOverlayGroup(polygonEntity)
    AddOverlayGroup(outlineEntity)
    initTextInfo(name, calcPolygonPosition(coordinates), data.id)
  }

  // 更新飞行区域多边形
  function updateFlightAreaPolygon(id: string, name: string, coordinates: GeojsonCoordinate[], enable: boolean, type: EFlightAreaType, show?: string) {
    const viewer = root.$viewer
    const elements = getElementFromMap(id)

    if (elements && elements.length > 0 && show !== "show") {
      let textElement = elements.find(ele => ele.properties?.getValue()?.type === 'text')
      let polygonElement = elements.find(ele => ele.properties?.getValue()?.type === 'flightAreaPolygon')
      let outlineElement = elements.find(ele => ele.properties?.getValue()?.type === 'flightAreaPolygonOutline')

      const centerPosition = calcPolygonPosition(coordinates)

      if (!textElement) {
        initTextInfo(name, centerPosition, id)
      } else {
        textElement.position = Cesium.Cartesian3.fromDegrees(centerPosition[0], centerPosition[1])
        if (textElement.label) {
          textElement.label.text = name
        }
      }

      if (polygonElement && polygonElement.polygon) {
        const positions = coordinates.map(coordinate =>
          Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
        )

        polygonElement.polygon.hierarchy = new Cesium.PolygonHierarchy(positions)
        polygonElement.polygon.material = Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(0)
        polygonElement.polygon.outlineColor = Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor)
        polygonElement.polygon.outlineWidth = 8
      }

      if (outlineElement && outlineElement.polyline) {
        const positions = coordinates.map(coordinate =>
          Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
        )
        // 闭合多边形轮廓
        outlineElement.polyline.positions = [...positions, positions[0]]
        outlineElement.polyline.material = Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(1.0)
      } else {
        // 如果没有轮廓实体，创建一个
        const positions = coordinates.map(coordinate =>
          Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1])
        )

        const outlineEntity = viewer.entities.add({
          id: id + '-outline',
          polyline: {
            positions: [...positions, positions[0]], // 闭合多边形轮廓
            width: 3,
            material: Cesium.Color.fromCssColorString(enable ? flightAreaColorMap[type] : disableColor).withAlpha(1.0),
            clampToGround: true,
            classificationType: Cesium.ClassificationType.BOTH,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, Number.MAX_VALUE)
          },
          properties: {
            id: id,
            type: 'flightAreaPolygonOutline'
          }
        })
        AddOverlayGroup(outlineEntity)
      }
    } else {
      initFlightAreaPolygon(name, coordinates, { id, type, enable })
    }
  }

  // 隐藏/显示覆盖物
  function hideCoverFromMap(id: string, type: number) {
    const viewer = root.$viewer
    const elements = getElementFromMap(id)

    // 隐藏/显示主要元素
    if (elements && elements.length > 0) {
      elements.forEach(element => {
        element.show = type !== 1
      })
    }

    // 隐藏/显示对应的文本元素（文本元素ID为id+'-text'）
    const textEntityId = id + '-text'
    const textEntity = viewer.entities.getById(textEntityId)
    if (textEntity) {
      textEntity.show = type !== 1
    }
  }

  // 隐藏/显示所有覆盖物
  function hideAllCovers(type: number) {
    for (const id in coverMap) {
      hideCoverFromMap(id, type)
    }
  }

  return {
    init2DPin,
    initPolyline,
    initPolygon,
    removeCoverFromMap,
    getElementFromMap,
    updatePinElement,
    updatePolylineElement,
    updatePolygonElement,
    initFlightAreaCircle,
    initFlightAreaPolygon,
    updateFlightAreaPolygon,
    updateFlightAreaCircle,
    calcPolygonPosition,
    initTextInfo,
    clearTextInfo,
    clearAllTextInfo,
    hideCoverFromMap,
    hideAllCovers
  }
} 