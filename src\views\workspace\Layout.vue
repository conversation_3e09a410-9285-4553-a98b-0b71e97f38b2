<template>
  <div class="cockpit-container">
    <left-sider />
    <router-view />
    <!-- <div id="cockpitMap" class="map-view"></div> -->
    <div class="map-view">
      <Djimap />
    </div>
  </div>
</template>

<script setup>
import LeftSider from '@/components/left-sider/main.vue';
import Djimap from '@/components/djimap/djimap.vue';
</script>

<style lang="scss" scoped>
.cockpit-container {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #232323;
  color: #e0e0e0;
  overflow: hidden;
}

.map-view {
  flex-grow: 1;
  height: 100%;
  position: relative;
}

:deep(.cesium-viewer-bottom) {
  display: none;
}
</style>