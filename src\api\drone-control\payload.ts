import request from '@/axios'
import {GimbalResetMode} from '@/types/drone-control'
import {CameraType, CameraMode} from '@/types/live-stream'

export interface IResult {
  code: number;
  message: string;
}

export interface IPage {
  page: number;
  total: number;
  page_size: number;
}

export interface IListWorkspaceResponse<T> {
  code: number;
  message: string;
  data: {
    list: T[];
    pagination: IPage;
  };
}

// Workspace
export interface IWorkspaceResponse<T> {
  code: number;
  data: T;
  message: string;
}

export type IStatus = 'WAITING' | 'DOING' | 'SUCCESS' | 'FAILED';

export interface CommonListResponse<T> extends IResult {
  data: {
    list: T[];
    pagination: IPage;
  };
}

export interface CommonResponse<T> extends IResult {
  data: T
}

const API_PREFIX = '/hztech-flight-core/control/api/v1'

export interface PostPayloadAuthBody {
  payload_index: string
}

// 获取负载控制权
export async function postPayloadAuth(sn: string, body: PostPayloadAuthBody): Promise<IWorkspaceResponse<null>> {
  const resp = await request.post(`${API_PREFIX}/devices/${sn}/authority/payload`, body)
  return resp.data
}


// 获取云端授权状态
export async function getCloudAuthStatus(sn: string): Promise<IWorkspaceResponse<null>> {
  const resp = await request.post(`${API_PREFIX}/rcDevices/${sn}/getCloudAuthStatus`)
  return resp.data
}

// 获取云端控制权
export async function postCloudControlAuth(sn: string, body: any): Promise<IWorkspaceResponse<null>> {
  const resp = await request.post(`${API_PREFIX}/rcDevices/${sn}/cloudControl/flight?userId=${body.userId}&userCallsign=${body.userCallsign}`)
  return resp.data
}

// 解除云端控制
export async function postCloudControlRelease(sn: string): Promise<IWorkspaceResponse<null>> {
  const resp = await request.post(`${API_PREFIX}/rcDevices/${sn}/cloudControlRelease`)
  return resp.data
}

// 发送负载名称
export async function postPayloadCommands(sn: string, body: PostPayloadCommandsBody): Promise<IWorkspaceResponse<null>> {
  const resp = await request.post(`${API_PREFIX}/devices/${sn}/payload/commands`, body)
  return resp.data
}

// TODO: 画面拖动控制
export enum PayloadCommandsEnum {
  CameraModeSwitch = 'camera_mode_switch',
  CameraPhotoTake = 'camera_photo_take',
  CameraRecordingStart = 'camera_recording_start',
  CameraRecordingStop = 'camera_recording_stop',
  CameraFocalLengthSet = 'camera_focal_length_set',
  GimbalReset = 'gimbal_reset',
  CameraAim = 'camera_aim',
  CameraExposure = 'camera_exposure_mode_set',
  CameraExposureValue = 'camera_exposure_set',
  CameraFocus = 'camera_focus_mode_set',
  CameraFocusValue = 'camera_focus_value_set',
  CameraThermometric = 'ir_metering_mode_set',
  CameraSplitscreen = 'camera_screen_split',
  CameraLinkagezoom = 'drc_linkage_zoom_set',
  CameraPhotostorage = 'photo_storage_set',
  CameraVideostorage = 'video_storage_set',
  CameraThermometricPoint = 'ir_metering_point_set',
  CameraThermometricArea = 'ir_metering_area_set',
}

export interface PostCameraModeBody {
  payload_index: string
  camera_mode: CameraMode
}

export interface PostCameraPhotoBody {
  payload_index: string
}

export interface PostCameraRecordingBody {
  payload_index: string
}

export interface DeleteCameraRecordingParams {
  payload_index: string
}

export interface PostCameraFocalLengthBody {
  payload_index: string,
  camera_type: CameraType,
  zoom_factor: number
}

export interface PostGimbalResetBody {
  payload_index: string,
  reset_mode: GimbalResetMode,
}

export interface PostCameraAimBody {
  payload_index: string,
  camera_type: CameraType,
  locked: boolean,
  x: number,
  y: number,
}

export interface PostCameraExposureBody {
  payload_index: string,
  camera_type: CameraType,
  exposure_mode: CameraMode,
}

export interface PostCameraExposureValueBody {
  payload_index: string,
  camera_type: CameraType,
  exposure_value: string,
}

export interface PostCameraFocusBody {
  payload_index: string,
  camera_type: CameraType,
  focus_mode: string,
}

export interface PostCameraFocusValueBody {
  payload_index: string,
  camera_type: CameraType,
  focus_value: string,
}

export interface PostCameraThermometricBody {
  payload_index: string,
  mode: number,
}

export interface PostCameraPhotostorageBody {
  payload_index: string,
  photo_storage_settings: any,
}

export interface PostCameraVideostorageBody {
  payload_index: string,
  video_storage_settings: any,
}

export interface PostCameraLinkagezoomBody {
  payload_index: string,
  state: any,
}

export interface PostCameraSplitscreenBody {
  payload_index: string,
  enable: any,
}

export interface PostCameraThermometricPointBody {
  payload_index: string,
  x: number,
  y: number,
}

export interface PostCameraThermometricAreaBody {
  payload_index: string,
  x: number,
  y: number,
  width: number,
  height: number,
}

export type PostPayloadCommandsBody = {
  cmd: PayloadCommandsEnum.CameraModeSwitch,
  data: PostCameraModeBody
} | {
  cmd: PayloadCommandsEnum.CameraPhotoTake,
  data: PostCameraPhotoBody
} | {
  cmd: PayloadCommandsEnum.CameraRecordingStart,
  data: PostCameraRecordingBody
} | {
  cmd: PayloadCommandsEnum.CameraRecordingStop,
  data: DeleteCameraRecordingParams
} | {
  cmd: PayloadCommandsEnum.CameraFocalLengthSet,
  data: PostCameraFocalLengthBody
} | {
  cmd: PayloadCommandsEnum.GimbalReset,
  data: PostGimbalResetBody
} | {
  cmd: PayloadCommandsEnum.CameraAim,
  data: PostCameraAimBody
} | {
  cmd: PayloadCommandsEnum.CameraExposure,
  data: PostCameraExposureBody
} | {
  cmd: PayloadCommandsEnum.CameraExposureValue,
  data: PostCameraExposureValueBody
} | {
  cmd: PayloadCommandsEnum.CameraFocus,
  data: PostCameraFocusBody
} | {
  cmd: PayloadCommandsEnum.CameraFocusValue,
  data: PostCameraFocusValueBody
} | {
  cmd: PayloadCommandsEnum.CameraThermometric,
  data: PostCameraThermometricBody
} | {
  cmd: PayloadCommandsEnum.CameraPhotostorage,
  data: PostCameraPhotostorageBody
} | {
  cmd: PayloadCommandsEnum.CameraVideostorage,
  data: PostCameraVideostorageBody
} | {
  cmd: PayloadCommandsEnum.CameraLinkagezoom,
  data: PostCameraLinkagezoomBody
} | {
  cmd: PayloadCommandsEnum.CameraSplitscreen,
  data: PostCameraSplitscreenBody
} | {
  cmd: PayloadCommandsEnum.CameraThermometricPoint,
  data: PostCameraThermometricPointBody
} | {
  cmd: PayloadCommandsEnum.CameraThermometricArea,
  data: PostCameraThermometricAreaBody
}