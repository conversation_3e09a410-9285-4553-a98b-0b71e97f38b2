<template>
    <div class="fh2-container-wrapper">
        <!-- ******* 接入步骤 2 - START：提供组件所在的容器 ******* -->
        <div id="wayline-create-app-container"></div>
        <!-- ******* 接入步骤 2 - END：提供组件所在的容器 ******* -->

        <!-- <div class="btn-bar">
            <button @click="openCreate">打开创建航线</button>
            <button @click="changeTheme">样式定制</button>
        </div> -->
    </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';
import { fh2Config } from '@/config/fh2.js';

// 定义emit事件
const emit = defineEmits(['close']);

// ******* 接入步骤 6（按需） - START：自定义主题 *******
function changeTheme() {
    document.body.classList.toggle('set-change-color');
}
// ******* 接入步骤 6（按需） - END：自定义主题 *******

function openCreate() {
    if (!window.FH2) {
        console.error("FH2 is not initialized.");
        return;
    }
    // ******* 接入步骤 4 - START：加载组件 *******
    window.FH2.loadWaylineCreation("wayline-create-app-container");
    // ******* 接入步骤 4 - END：加载组件 *******
}

onMounted(() => {
    // ******* 接入步骤 6（按需） - START：自定义主题 *******
    const styleLink = document.createElement('link');
    styleLink.id = 'custom-theme-style';
    styleLink.rel = 'stylesheet';
    styleLink.href = '/custom-style.css';
    document.head.appendChild(styleLink);
    // ******* 接入步骤 6（按需） - END：自定义主题 *******

    // ******* 接入步骤 1 - 引入 paas.js（假定已在 index.html 中引入） *******
    if (!window.FH2) {
        console.error('FH2 script loaded but window.FH2 is not available.');
        return;
    }

    // ******* 接入步骤 3 - START：使用组件前初始化配置（具体配置参数请按实际替换） *******
    window.FH2.initConfig({
        serverUrl: fh2Config.serverUrl,
        wssUrl: fh2Config.wssUrl,
        hostUrl: fh2Config.hostUrl,
        prjId: fh2Config.prjId,
        projectToken: fh2Config.projectToken,
    });
    // ******* 接入步骤 3 - END：使用组件前初始化配置 *******

    // ******* 接入步骤 4 - START：加载组件 *******
    window.FH2.loadWaylineCreation("wayline-create-app-container");
    // ******* 接入步骤 4 - END：加载组件 *******

    // ******* 接入步骤 5（按需） - START：监听组件事件 *******
    window.FH2.subscribe('wayline-creation-saved', () => {
        emit('editWayline');
    });
    window.FH2.subscribe('wayline-creation-cancel', () => {
        emit('close');
    });
    // ******* 接入步骤 5（按需） - END：监听组件事件 *******
});

onUnmounted(() => {
    const styleLink = document.getElementById('custom-theme-style');
    if (styleLink) {
        document.head.removeChild(styleLink);
    }
    document.body.classList.remove('set-change-color');

    // 确保在组件卸载时销毁所有创建的iframe和资源
    if (window.FH2 && typeof window.FH2.destroyWaylineCreation === 'function') {
        window.FH2.destroyWaylineCreation();
    }
    
    // 取消订阅事件，防止内存泄漏
    if (window.FH2 && typeof window.FH2.unsubscribe === 'function') {
        window.FH2.unsubscribe('wayline-creation-saved');
        window.FH2.unsubscribe('wayline-creation-cancel');
    }
});
</script>

<style lang="scss" scoped>
.fh2-container-wrapper {
    height: 100%;
    width: 100%;
    margin: 0;
}

#wayline-create-app-container {
    width: 100%;
    height: 100%;
}

:deep(#wayline-create-app-container>div) {
    width: 100%;
    height: 100%;
}

.btn-bar {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 9999;
    gap: 10px;
}
</style>